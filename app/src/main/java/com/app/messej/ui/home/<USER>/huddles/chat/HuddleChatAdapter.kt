package com.app.messej.ui.home.publictab.huddles.chat

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.media3.ui.PlayerView
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.MentionedUser
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.HuddleChatMessageWithMedia
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.databinding.ItemChatMessageHuddleBinding
import com.app.messej.ui.chat.adapter.ChatAdapter

open class HuddleChatAdapter(
    private val inflater: LayoutInflater,
    private val userId: Int,
    private var mListener: HuddleChatClickListener,
    private var userCitizenship : UserCitizenship?
    ): Chat<PERSON>dapter(inflater, userId, false, mListener) {

    open interface HuddleChatClickListener: ChatClickListener {
        fun onUpgradeClick()
        fun onFollowClick(msg: HuddleChatMessage)
        fun onMessageOptionsClick(msg: HuddleChatMessageWithMedia, position: Int, view: View)
        fun onStreamMedia(view: PlayerView, msg: AbstractChatMessageWithMedia, position: Int): Boolean { return false }
        fun onClickOnComments(item: HuddleChatMessage, position: Int) {}
        fun onClickOnMention(user: MentionedUser)

        fun onGiftClick(msg: HuddleChatMessage)

        fun goToGiftFile(isSelf:Boolean)
        fun goToIdCard(msg: HuddleChatMessage)

        fun onLivePodiumIndicatorClicked(msg: HuddleChatMessage){}
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChatViewHolder {
        return if(isOfMessageType(viewType)) {
            HuddleMessageViewHolder(ItemChatMessageHuddleBinding.inflate(inflater, parent, false), userId, mListener ,userCitizenship)
        }
        else super.onCreateViewHolder(parent, viewType)
    }
}