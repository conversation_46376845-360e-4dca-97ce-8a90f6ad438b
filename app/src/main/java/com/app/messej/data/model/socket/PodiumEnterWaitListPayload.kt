package com.app.messej.data.model.socket

import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.google.gson.annotations.SerializedName

data class PodiumEnterWaitListPayload(
    @SerializedName("podium_id"      ) val podiumId: String,

    @SerializedName("thumbnail"  ) val thumbnail  : String?  = null,
    @SerializedName("time_added" ) val timeAdded  : String?  = null,
    @SerializedName("username"   ) val username   : String?  = null,
    @SerializedName("id"         ) val id         : Int,
    @SerializedName("membership" ) val membership : UserType,
    @SerializedName("citizenship") val citizenship: UserCitizenship?,
    @SerializedName("verified"   ) val verified   : Boolean,
    @SerializedName("name"       ) val name       : String,
    @SerializedName("user_tribe_name") val tribeName: String?

): SocketEventPayload() {
    fun asSpeaker(): PodiumSpeaker = PodiumSpeaker(
        id = id,
        name = name,
        username = username,
        thumbnail = thumbnail,
        membership = membership,
        citizenship = citizenship,
        verified = verified,
        tribeName = tribeName
    )

    companion object {
        fun fromSpeaker(speaker: PodiumSpeaker, podiumId: String, timeAdded: String): PodiumEnterWaitListPayload {
            return PodiumEnterWaitListPayload(
                podiumId = podiumId,
                timeAdded = timeAdded,
                id = speaker.id,
                name = speaker.name,
                username = speaker.username,
                thumbnail = speaker.thumbnail,
                membership = speaker.membership,
                citizenship = speaker.citizenship,
                verified = speaker.verified,
                tribeName = speaker.tribeName
            )
        }
    }
}
