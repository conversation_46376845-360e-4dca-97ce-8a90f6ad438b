package com.app.messej.data.model.api.profile

import android.util.Log
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.profile.UserEnforcements.Companion.orEmpty
import com.app.messej.data.model.api.profile.UserEnforcements.EnforcementMeta
import com.app.messej.data.model.api.profile.UserEnforcements.EnforcementStatus
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.app.messej.data.utils.DateTimeUtils
import com.google.android.gms.maps.model.LatLng
import com.google.gson.annotations.SerializedName
import java.math.RoundingMode
import java.time.ZonedDateTime

data class AccountDetailsResponse(
    @SerializedName("id"                                 ) override val id                             : Int,
    @SerializedName("name"                               ) override val name                           : String            = "",
    @SerializedName("username"                           ) override val username                       : String,
//    @SerializedName("nicknames"                          ) val nicknames                      : A<PERSON>y<PERSON>ist<String> = arrayListOf(),
    @SerializedName("verified"                           ) override val verified                       : <PERSON><PERSON><PERSON>,
    @SerializedName("created_on"                         ) val createdOn                      : String?           = null,
    @SerializedName("email"                              ) override val email                          : String?           = null,
    @SerializedName("phone"                              ) override val phone                          : String?           = null,
    @SerializedName("country_code"                       ) override val countryCode                    : String?           = null,
    @SerializedName("country_code_iso"                   ) override val countryCodeIso                 : String?           = null,
    @SerializedName("show_country_flag"                  ) val showCountryFlag                : Boolean?          = true,
    @SerializedName("country"                            ) val country                        : String?           = null,
    @SerializedName("dob"                                ) val dob                            : String?           = null,
    @SerializedName("gender"                             ) val gender                         : String?           = null,
    @SerializedName("about"                              ) val about                          : String?           = null,
    @SerializedName("profile_photo"                      ) val profilePhoto                   : String?           = null,
    @SerializedName("thumbnail"                          ) override val thumbnail                      : String?           = null,
    @SerializedName("device"                             ) val device                         : String?           = null,
    @SerializedName("blocked"                            ) override val blockedByAdmin                        : Boolean?          = null,
    @SerializedName("account_status"                     ) val accountStatus                  : String?           = null,
    @SerializedName("payment_date"                       ) val paymentDate                    : String?           = null,
    @SerializedName("membership"                         ) override val membership                     : UserType = UserType.FREE,
    @SerializedName("subscription_status"                ) val subscriptionStatus             : String?           = null,
    @SerializedName("active_points"                      ) override var activePoints                  : Double?           = null,
    @SerializedName("inactive_points"                    ) private val _inactivePoints        : Double?           = null,
    @SerializedName("fans"                               ) override val fans                           : Int               = 0,
    @SerializedName("dears"                              ) override val dears                          : Int               = 0,
    @SerializedName("stars"                              ) override val stars                          : Int               = 0,
    @SerializedName("likers"                             ) override val likers                         : Int               = 0,
    @SerializedName("total_performance_points"           ) private val _totalPerformancePoints: Double?           = null,
    @SerializedName("is_premium"                         ) var isPremium                      : Boolean?          =null,
    @SerializedName("pp_rate"                            ) val ppRate                         : Double?           = null,
    @SerializedName("is_referrer"                        ) val isReferrer                     : Boolean?          = null,
    @SerializedName("location_skipped"                   ) val locationSkipped                : Boolean?          = null,
    @SerializedName("referral_link"                      ) val referralLink                   : String?           = null,
    @SerializedName("longitude"                          ) val longitude                      : Double?           = null,
    @SerializedName("latitude"                           ) val latitude                       : Double?           = null,
    @SerializedName("profile_complete_percentage"        ) override val profileCompletePercentage      : Int               = 0,
    @SerializedName("subscription_expiration_date"       ) private val _subscriptionExpirationDate     : String?           = null,
    @SerializedName("broadcast_delete_timeout"           ) private val _broadcastDeleteTimeout         : Float?              = null,
    @SerializedName("huddle_message_delete_timeout"      ) private val _huddleMessageDeleteTimeout     : Float?              = null,
    @SerializedName("private_message_delete_timeout"     ) private val _privateMessageDeleteTimeout    : Float?              = null,
    @SerializedName("total_broadcasts"                   ) val totalBroadcasts                : Int?              = null,
    @SerializedName("total_likes"                        ) val totalLikes                     : Int?              = null,
    @SerializedName("huddle_participants_limit_for_free" ) val huddleParticipantsLimitForFree : Int?              = null,
    @SerializedName("video_duration_limit"               ) val videoDurationLimit             : Float?              = null,
    @SerializedName("flash_duration"                     ) val flashDurationLimit             : Int?,
    @SerializedName("flax_increment"                ) var isFlaxIncrement        :  Boolean? = false,
    @SerializedName("flax_rate_percentage"          ) var flaxRatePercentage     :  Int?,
    @SerializedName("empowerments"                  ) override val userEmpowerment: CurrentUser.UserEmpowerment? = null,
    @SerializedName("total_gift_points"             ) override var totalGiftPoints        : Double? = 0.0,
    @SerializedName("total_received_points"                  ) var   totalPoints : String? = "0.00",
    @SerializedName("user_gift_sum"                 ) var userGiftSum : String? ="0.00",
    @SerializedName("citizenship"                        ) override val   citizenship: UserCitizenship,
    @SerializedName("remaining_days_for_resident"   ) var remainingDaysForResident :Int? = null,
    @SerializedName("total_received_points_thismonth") var totalRecievedPointsThisMonth : Double? = 0.0,
    @SerializedName("flash_block") override var isFlashBlocked : Boolean? = false,
    @SerializedName("user_level_animation_url") override var userLevelAnimationUrl : String? = null,
    @SerializedName("citizenship_priority") override var citizenshipPriority : Int? = null,
    @SerializedName("location_changed") override var locationChanged : Boolean? = false,
    @SerializedName("is_flix_subscription_renewed" ) override val isFlixSubscriptionRenewed : Boolean?          = null,
    @SerializedName("user_functionality_blocks"    ) override val userFunctionalityBlocks : CurrentUser.UserFunctionalityBlocks? = null,
    @SerializedName("coins_needed_next_contributor_level") override val coinsNeededNextContributorLevel : Double?=0.0,
    @SerializedName("games_needed_next_player_level") override val gamesNeededNextPlayerLevel : Double?=0.0,
    @SerializedName("citizenship_since" ) val citizenshipSince: String? = null,

    @SerializedName("enforcements_status") override var enforcementStatus: EnforcementStatus?,
    @SerializedName("enforcements_meta") var enforcementsMeta: EnforcementMeta?,
    @SerializedName("superstar_id") override val superStarId: Int? = null,
    @SerializedName("user_tribe_name")  val userTribeName: String? = null

): CurrentUserAndAccountDetails() {

    private val citizenshipUpdatedZonedDateTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(citizenshipSince)

    val formatedCitizenshipUpdatedDate: String?
        get() = DateTimeUtils.format(citizenshipUpdatedZonedDateTime, format = DateTimeUtils.FORMAT_DDMMYYYY_SLASHED)

   val subscriptionExpirationDate:ZonedDateTime?
     get()=DateTimeUtils.parseZonedDateTime(_subscriptionExpirationDate,"EEE, dd MMM yyyy HH:mm:ss zzz")

    val location: LatLng?
        get() {
            latitude?: return null
            longitude?: return null
            return LatLng(latitude, longitude)
        }

    val activePointsString: String?
        get() = activePoints?.toBigDecimal()?.setScale(2, RoundingMode.HALF_UP)?.toString()

    val currentFlix:Double
        get()  = activePoints?: 0.0
    val currentCoin:Double
        get()  = totalGiftPoints?: 0.0

    val inactivePoints: Double?
        get() = _inactivePoints?.toBigDecimal()?.setScale(2, RoundingMode.FLOOR)?.toDouble()

    val totalPerformancePoints: Double?
        get() = _totalPerformancePoints?.toBigDecimal()?.setScale(2, RoundingMode.FLOOR)?.toDouble()

    val huddleMessageDeleteTimeoutInSeconds: Long?
        get() {
            Log.w("BCVM", "_huddleMessageDeleteTimeout: $_huddleMessageDeleteTimeout")
            return _huddleMessageDeleteTimeout?.times(60)?.toLong()
        }
    val privateMessageDeleteTimeoutInSeconds: Long?
        get() = _privateMessageDeleteTimeout?.times(60)?.toLong()
    val broadcastDeleteTimeoutInSeconds: Long?
        get() = _broadcastDeleteTimeout?.times(60)?.toLong()
    val flaxRate: Double
        get() {
            return (flaxRatePercentage?:0).toDouble()/100
        }

    var enforcements: UserEnforcements
        get() = UserEnforcements(id, enforcementStatus.orEmpty(), enforcementsMeta.orEmpty())
        set(value) {
            enforcementStatus = value.enforcementsStatus
            enforcementsMeta = value.enforcementsMeta
        }
}
