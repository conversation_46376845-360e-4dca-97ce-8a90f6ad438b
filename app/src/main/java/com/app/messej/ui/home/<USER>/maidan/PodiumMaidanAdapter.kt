package com.app.messej.ui.home.publictab.maidan

import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.entity.Podium
import com.app.messej.databinding.ItemPodiumListMaidanBinding
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences

class PodiumMaidanAdapter( private val listener: PodiumActionListener): PagingDataAdapter<Podium,PodiumMaidanAdapter.PodiumListViewHolder>(PodiumDiff) {

    interface PodiumActionListener {
        fun onPodiumClicked(pod: Podium)
    }

    inner class PodiumListViewHolder(private val binding:ItemPodiumListMaidanBinding):RecyclerView.ViewHolder(binding.root){
        fun bind(item: Podium) = with(binding) {
            podium = item
            podiumName.text = if (item.hasCompetitor) {
                val str = root.resources.getString(R.string.podium_maidan_vs,item.managerName,item.competitorName.orEmpty())
                str.highlightOccurrences(item.managerName) {
                    ForegroundColorSpan(ContextCompat.getColor(root.context, R.color.colorPrimary))
                }
            } else item.managerName

            card.setOnClickListener {
                listener.onPodiumClicked(item)
            }
            userAction.setOnClickListener {
                listener.onPodiumClicked(item)
            }
        }
    }
    object PodiumDiff : DiffUtil.ItemCallback<Podium>(){
        override fun areItemsTheSame(oldItem: Podium, newItem: Podium) = oldItem.id == newItem.id
        override fun areContentsTheSame(oldItem: Podium, newItem: Podium) = oldItem == newItem
    }

    override fun onBindViewHolder(holder: PodiumListViewHolder, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PodiumListViewHolder {
        val binding = ItemPodiumListMaidanBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return PodiumListViewHolder(binding)
    }
}