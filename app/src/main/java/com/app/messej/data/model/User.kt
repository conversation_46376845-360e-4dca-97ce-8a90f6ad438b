package com.app.messej.data.model

import androidx.room.ColumnInfo
import androidx.room.PrimaryKey
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.google.gson.annotations.SerializedName

data class User(
    @SerializedName("id"            ) @ColumnInfo(name = "id"                   ) @PrimaryKey(autoGenerate = false) override val id           : Int = 0,
    @SerializedName("name"          ) @ColumnInfo(name = "name"                 ) override val name         : String = "",
    @SerializedName("thumbnail"     ) @ColumnInfo(name = "thumbnail"            ) override val thumbnail    : String? = null,
    @SerializedName("username"      ) @ColumnInfo(name = "username"             ) override val username     : String = "",

    @SerializedName("membership"    ) @ColumnInfo(name = "membership"           ) override val membership   : UserType = UserType.FREE,
    @SerializedName("verified"      ) @ColumnInfo(name = "verified"             ) override val verified     : Boolean = false,
    @SerializedName("blocked"       ) @ColumnInfo(name = "blocked"              )          val blocked      : Boolean = false,

    @SerializedName("dears"         ) @ColumnInfo(name = "dears"                ) override val dears        : Int     = 0,
    @SerializedName("fans"          ) @ColumnInfo(name = "fans"                 ) override val fans         : Int     = 0,
    @SerializedName("likers"        ) @ColumnInfo(name = "likers"               ) override val likers       : Int     = 0,
    @SerializedName("stars"         ) @ColumnInfo(name = "stars"                ) override val stars        : Int     = 0,
    
    @SerializedName("is_referrer"   ) @ColumnInfo(name = "isRefferer"           )          val isReferrer   : Boolean = false,
    @SerializedName("citizenship"   ) @ColumnInfo(name = "citizenship"          ) override val citizenship  : UserCitizenship? = null
): AbstractUserWithStats() {
}
