package com.app.messej.ui.home.publictab.huddles.chat

import android.content.Context
import android.text.SpannableString
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import com.app.messej.R
import com.app.messej.data.model.ReplyTo
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.HuddleChatMessageWithMedia
import com.app.messej.data.model.enums.ChatBubbleMediaState
import com.app.messej.data.model.enums.FollowerType
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserRole
import com.app.messej.databinding.ItemChatMessageHuddleBinding
import com.app.messej.databinding.ItemChatMessageHuddleReplyToBinding
import com.app.messej.databinding.ItemChatMessageMediaVideoBinding
import com.app.messej.ui.chat.ChatMessageUIModel
import com.app.messej.ui.chat.adapter.ChatMessageViewHolder
import com.app.messej.ui.utils.EnumUtils.displayText
import com.app.messej.ui.utils.TextFormatUtils.applyMarkdownFormatting
import com.app.messej.ui.utils.TextFormatUtils.highlightMentions
import com.app.messej.ui.utils.TextFormatUtils.setFormattedText

open class HuddleMessageViewHolder(
    val binding: ItemChatMessageHuddleBinding,
    userId: Int,
    private var mListener: HuddleChatAdapter.HuddleChatClickListener,
    private var userCitizenship: UserCitizenship?,
) : ChatMessageViewHolder(binding.root, userId, false, mListener) {

    private var huddleReplyBinding: ItemChatMessageHuddleReplyToBinding? = null

    private var isGiftClickBlocked: Boolean = false

    override fun bind(item: ChatMessageUIModel): Unit = with(binding) {
        super.bind(item)
        val cm = (item as ChatMessageUIModel.ChatMessageModel)

        val hcm = cm.message as HuddleChatMessage
        message = hcm
        selected = item.selected

        val isSelfMessage = (hcm.sender == userId)
        val canUpgrade = isSelfMessage && hcm.senderDetails?.premium != true
        isOwnMessage = isSelfMessage
        senderName = if (isSelfMessage) root.context.getString(R.string.common_you) else (cm.chat as HuddleChatMessageWithMedia).senderNickNameOrName
        showUpgrade = canUpgrade
        relation = when (hcm.senderRelation) {
            FollowerType.DEAR -> root.context.getString(R.string.profile_dear)
            FollowerType.FAN -> root.context.getString(R.string.profile_fan)
            FollowerType.LIKER -> root.context.getString(R.string.profile_liker)
            else -> null
        }
        //Gift

        binding.isGiftBlocked = hcm.let {
            it.senderDetails?.let { senderDetails ->
                val isGolden = senderDetails.citizenship?.isGolden?:false
                senderDetails.blockedByEitherAdmin || senderDetails.deletedAccount || hcm.reported || senderDetails.blockedByHuddleAdmin == true /*|| userCitizenship == UserCitizenship.VISITOR*/|| isGolden
            } ?: false
        }

        isGiftClickBlocked = hcm.let {
            it.senderDetails?.let { senderDetails ->
                val isGolden = senderDetails.citizenship?.isGolden?:false
                senderDetails.blockedByEitherAdmin || senderDetails.deletedAccount || isGolden || isSelfMessage || hcm.reported || senderDetails.blockedByHuddleAdmin == true /*|| userCitizenship == UserCitizenship.VISITOR*/
            } ?: false
        }


        showFollow = !hcm.isStar && !isSelfMessage && hcm.senderDetails?.deletedAccount == false
        if (showFollow == true) showUpgrade = false

        val color = setChatBubbleColor(binding.chatBubble, cm)

        if (hcm.senderDetails?.blockedByEitherAdmin == true) {
            Log.w("HMVH", "bind: user is blocked by admin")
            chatMessage.setText(R.string.chat_message_admin_kicked)
        } else if (hcm.reported) {
            chatMessage.setText(R.string.chat_post_reported)
        } else {
            val messageSpan = formatAndHighlightText(chatMessage.context, cm)
            chatMessage.setText(messageSpan ?: "", TextView.BufferType.SPANNABLE)
            chatMessage.movementMethod = LinkMovementMethod.getInstance()
            chatMessage.setExpanded(false)
            chatMessage.apply {
                setOnStateChangeListener { expanded ->
                    cm.expanded = expanded
                }
                setExpanded(cm.expanded)
            }
            loadMedia(mediaHolder, cm, color)
        }
        loadReply(replyHolder, cm, color)

        chatFlag.setImageResource(cm.countryFlag ?: 0)
        giftHuddle.setOnClickListener {
            if (userCitizenship?.isVisitor == true) return@setOnClickListener
            if (!isGiftClickBlocked) mListener.onGiftClick(hcm)
            if (isSelfMessage) mListener.goToGiftFile(true) else mListener.goToGiftFile(false)
        }
        binding.userDp.setOnClickListener {
            if(message?.senderDetails?.userLivePodium == true && message?.senderDetails?.userLivePodiumId!=null && !isSelfMessage){
                mListener.onLivePodiumIndicatorClicked(hcm)
            }
            else mListener.goToIdCard(hcm)
        }

        chatHolder.setOnClickListener { mListener.onItemClick(item.message, layoutPosition) }
        chatHolder.setOnLongClickListener {
            if (hcm.senderDetails?.blockedByEitherAdmin != true) {
                mListener.onItemLongClick(hcm, layoutPosition)
                true
            } else false
        }

        chatMessage.setOnClickListener {
            if (mListener.onItemClick(hcm, layoutPosition)) return@setOnClickListener
            if (chatMessage.isExpanded()) return@setOnClickListener
            chatMessage.setExpanded(true)
        }
        chatMessage.setOnLongClickListener {
            if (hcm.senderDetails?.blockedByEitherAdmin != true) {
                mListener.onItemLongClick(hcm, layoutPosition)
                true
            } else false
        }
        chatActions.setOnClickListener {
            mListener.onMessageOptionsClick(cm.chat as HuddleChatMessageWithMedia, layoutPosition, chatActions)
        }


        chatActions.isVisible = hcm.senderDetails?.blockedByEitherAdmin != true || hcm.sender == userId

        /*    replyIcon.setOnClickListener {
                mListener.onItemReply(hcm, layoutPosition)
            }
            replyIcon.isVisible = hcm.senderDetails?.blockedByEitherAdmin!=true && hcm.sendStatus==AbstractChatMessage.SendStatus.NONE

            if (!isSelfMessage && !hcm.reported && hcm.senderDetails?.blockedByEitherAdmin!=true) {
                likeButton.isClickable = true
                likeButton.setOnClickListener {
                    mListener.onItemLike(hcm, layoutPosition)
                }
                likeButton.setIconResource(if (hcm.liked) R.drawable.ic_chat_liked else R.drawable.ic_chat_like)
            } else {
                likeButton.isClickable = false
                likeButton.setOnClickListener(null)
                likeButton.setBackgroundResource(0)
                likeButton.icon = null
            }*/

        commentsLayout.isVisible = hcm.senderDetails?.premium == true
        commentsLayout.setOnClickListener {
            mListener.onClickOnComments(hcm, layoutPosition)
        }

        if (canUpgrade) {
            upgradeButton.setOnClickListener {
                mListener.onUpgradeClick()
            }
        }

        userFollow.setOnClickListener {
            mListener.onFollowClick(hcm)
        }


        var textColor = R.color.colorPrimary
        var borderColor = R.color.colorPrimary
        var backgroundColor = R.color.colorSecondaryLight

        Log.w("HMVM", "citizenship: ${hcm.senderDetails?.citizenship}")

        var notchText = root.resources.getString(hcm.senderDetails?.citizenship?.displayText()?:0)

        when (hcm.senderDetails?.citizenship) {
            UserCitizenship.VISITOR -> {
                textColor = R.color.textColorOnSecondary
                borderColor = R.color.textColorSecondaryLight
                backgroundColor = R.color.colorSurfaceSecondary
            }
            UserCitizenship.RESIDENT -> {
                textColor = R.color.textColorBusinessPrimary
                borderColor = R.color.textColorOnSecondary
                backgroundColor = R.color.colorHuddleResidentBackground
            }
            UserCitizenship.CITIZEN -> {
                textColor = R.color.colorPrimaryColorDarkest1
                borderColor = R.color.colorPrimaryColorDarkest1
                backgroundColor = R.color.colorHuddleTagCitizen
            }
            UserCitizenship.OFFICER -> {
                textColor = R.color.colorHuddleTagOfficerBorder
                borderColor = R.color.colorHuddleTagOfficerBorder
                backgroundColor = R.color.colorHuddleOfficerBackground
            }
            UserCitizenship.AMBASSADOR -> {
                textColor = R.color.colorPrimaryColorDarkest1
                borderColor = R.color.colorPrimaryColorDarkest1
                backgroundColor = R.color.colorHuddleAmbassadorBackground
            }
            UserCitizenship.MINISTER -> {
                textColor = R.color.colorAlwaysLightSurface
                borderColor = R.color.colorPrimary
                backgroundColor = R.color.colorPrimary
            }
            UserCitizenship.PRESIDENT -> {
                textColor = R.color.colorPrimaryColorDarkest1
                borderColor = R.color.colorPrimaryColorDarkest1
                backgroundColor = R.color.colorPresidentHuddleTagGolden
            }
            UserCitizenship.GOLDEN -> {
                textColor = R.color.colorFlashatGolden
                borderColor = R.color.colorFlashatGolden
                backgroundColor = R.color.colorPresidentHuddleTagGolden
            }
            null -> {
                textColor = R.color.colorPrimary
                borderColor = R.color.colorPrimary
                backgroundColor = R.color.colorSecondary
            }
        }

        headerNotch.headerBorderColor = ContextCompat.getColor(root.context, borderColor)
        headerNotch.headerBackgroundColor = ContextCompat.getColor(root.context, backgroundColor)
        headerNotch.headerTextColor = ContextCompat.getColor(root.context, textColor)

        if (hcm.senderDetails?.priority != null) {
            val res = root.resources.getString(if (hcm.senderDetails?.priority == UserRole.MANAGER) R.string.chat_huddle_manager else R.string.chat_huddle_admin)
            notchText = if (notchText == null) res else root.resources.getString(R.string.chat_huddle_header_joiner, notchText, res)
        }
        headerNotch.headerText = notchText.orEmpty().uppercase()
    }

    companion object {
        fun setChatBubbleColor(chatBubble: View, isPremium: Boolean, isSelf: Boolean): Int {

            Log.d("HMVH", "setChatBubbleColor: $isPremium $isSelf")
            val background: Int
            val color: Int
            if (isPremium) {
                if (isSelf) {
                    background = R.drawable.bg_chat_bubble_huddle_outgoing
                    color = R.color.colorChatOutgoingHuddle
                    Log.d("HMVH", "setChatBubbleColor: Pink")
                } else {
                    background = R.drawable.bg_chat_bubble_huddle_incoming
                    color = R.color.colorChatIncomingHuddle
                    Log.d("HMVH", "setChatBubbleColor: Yellow")
                }
            } else {
                background = R.drawable.bg_chat_bubble_huddle_default
                color = R.color.colorChatDefaultHuddle
                Log.d("HMVH", "setChatBubbleColor: Grey")
            }

            chatBubble.setBackgroundResource(background)
            return color
        }

        fun setPinnedChatBubbleColor(chatBubble: View, isPremium: Boolean, isSelf: Boolean): Int {

            Log.d("HMVH", "setChatBubbleColor: $isPremium $isSelf")
            val background: Int
            val color: Int
            if (isSelf) {
                background = R.drawable.bg_chat_bubble_pinned_huddle_outgoing
//                    color = R.color.colorChatPremiumHuddle
                color = R.color.colorChatOutgoingPinnedHuddle
                Log.d("HMVH", "setChatBubbleColor: Pink")
            } else {
                background = R.drawable.bg_chat_bubble_pinned_huddle_incoming
//                    color = R.color.colorChatAdminHuddle
                color = R.color.colorChatIncomingPinnedHuddle
                Log.d("HMVH", "setChatBubbleColor: Yellow")
            }

            chatBubble.setBackgroundResource(background)
            return color
        }

        fun formatAndHighlight(c: Context, hcm: HuddleChatMessage, userId: Int): Spanned? {
            val messageSpan = hcm.displayMessage?.applyMarkdownFormatting(c)
            return if (hcm.hasMention) {
                messageSpan?.highlightMentions(c, hcm.mentionedUsers.orEmpty(), userId)
            } else messageSpan
        }

        fun formatAndHighlight(c: Context, reply: ReplyTo, userId: Int): Spanned? {
            val messageSpan = reply.message?.applyMarkdownFormatting(c)
            return if (reply.hasMention) {
                messageSpan?.highlightMentions(c, reply.mentionedUsers.orEmpty(), userId)
            } else messageSpan
        }

    }

    override fun setChatBubbleColor(chatBubble: View, cm: ChatMessageUIModel.ChatMessageModel): Int {
        val msg = cm.message as HuddleChatMessage
        val isSelfMessage = (msg.sender == userId)
        return setChatBubbleColor(chatBubble, msg.senderDetails?.premium == true, isSelfMessage)
    }

    override fun formatAndHighlightText(c: Context, msg: ChatMessageUIModel.ChatMessageModel): SpannableString? {
        val messageSpan = super.formatAndHighlightText(c, msg)
        val hcm = msg.message
        return if (hcm is HuddleChatMessage && hcm.hasMention) {
            messageSpan?.highlightMentions(c, hcm.mentionedUsers.orEmpty(), userId) {
                mListener.onClickOnMention(it)
            }
        } else messageSpan
    }

    override fun loadReply(replyHolder: ViewGroup, cm: ChatMessageUIModel.ChatMessageModel, bubbleColor: Int) {
        replyHolder.visibility = if (cm.message.hasReply && !cm.message.reported) View.VISIBLE else View.GONE
        cm.message.replyTo?.let { reply ->
            if (huddleReplyBinding == null) {
                replyHolder.removeAllViews()
                val ab = ItemChatMessageHuddleReplyToBinding.inflate(LayoutInflater.from(replyHolder.context), replyHolder, false)
                replyHolder.addView(ab.root)
                huddleReplyBinding = ab
            }
            val hcm = (cm.message as HuddleChatMessage)
            val isSelfMessage = (reply.senderId == userId)
            huddleReplyBinding?.apply {
                replyTo = reply
                senderName = if (isSelfMessage) root.context.getString(R.string.common_you) else (cm.chat as HuddleChatMessageWithMedia).replySenderNickNameOrName
                relation = if (isSelfMessage) null else when (hcm.senderRelation) {
                    FollowerType.DEAR -> root.context.getString(R.string.profile_dear)
                    FollowerType.FAN -> root.context.getString(R.string.profile_fan)
                    FollowerType.LIKER -> root.context.getString(R.string.profile_liker)
                    else -> null
                }
                replyLayout.isClickable = true
                replyLayout.setOnClickListener { mListener.onItemReplyClick(cm.message, layoutPosition) }
                setChatBubbleColor(chatBubble, reply.premium, isSelfMessage)

                chatMessage.apply {
                    if (reply.mediaType == MediaType.AUDIO) {
                        text = chatMessage.context.getString(R.string.message_list_preview_audio, reply.mediaDuration)
                    } else if (reply.hasMention) {
                        text = formatReplyText(root.context, reply)?.highlightMentions(root.context, reply.mentionedUsers.orEmpty(), userId)
                    } else {
                        this.setFormattedText(reply.message ?: "", reply.chatTextColor)
                    }
                }
            }
        }
    }

    override fun loadMediaVideo(binding: ItemChatMessageMediaVideoBinding, msg: ChatMessageUIModel.ChatMessageModel) {
        super.loadMediaVideo(binding, msg)

        if (binding.mediaState == ChatBubbleMediaState.DOWNLOAD) {
            binding.mediaState = ChatBubbleMediaState.LOCAL
            binding.playButton.setOnClickListener {
                mListener.onStreamMedia(binding.playerView, msg.chat, layoutPosition)
                binding.mediaState = ChatBubbleMediaState.PREPARING
            }
        }
    }

    override fun getHighlightView() = binding.highlightView
}