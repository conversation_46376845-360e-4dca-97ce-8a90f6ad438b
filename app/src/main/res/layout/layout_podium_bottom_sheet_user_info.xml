<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="speaker"
            type="com.app.messej.data.model.AbstractUser" />
        <variable
            name="stats"
            type="com.app.messej.data.model.api.podium.PodiumUserLikesCoinsResponse" />
        <variable
            name="userTribeName"
            type="String" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/activity_margin"
        android:animateLayoutChanges="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/handle"
        tools:showIn="@layout/fragment_podium_speaker_actions_bottom_sheet">

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/user_dp"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginHorizontal="@dimen/activity_margin"
            android:layout_marginVertical="@dimen/activity_margin"
            android:scaleType="centerCrop"
            app:imageUrl="@{speaker.thumbnail}"
            app:layout_constraintBaseline_toBottomOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:placeholder="@{@drawable/im_user_placeholder_square}"
            app:riv_oval="true"
            tools:src="@drawable/im_user_placeholder_square" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/superstar_premium_badge"
            android:layout_width="@dimen/premium_badge_size"
            android:layout_height="@dimen/premium_badge_size"
            android:elevation="4dp"
            app:layout_constraintStart_toStartOf="@id/user_dp"
            app:layout_constraintTop_toTopOf="@id/user_dp"
            app:userBadgeOnPrimary="@{speaker.userBadge}"
            tools:src="@drawable/ic_user_badge_premium" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/user_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/activity_margin"
            android:layout_marginEnd="@dimen/element_spacing"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@{speaker.name}"
            android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
            android:textColor="@color/textColorSecondary"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/user_dp"
            app:layout_constraintTop_toTopOf="@+id/user_dp"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="Esther Lopez Desperanzo" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/message"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="4"
            android:text="@{speaker.username}"
            android:textAppearance="@style/TextAppearance.Flashat.Label.Small"
            android:textColor="@color/textColorSecondary"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="@+id/user_name"
            app:layout_constraintStart_toStartOf="@+id/user_name"
            app:layout_constraintTop_toBottomOf="@+id/user_name"
            tools:text="esther.lopz" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/tribe_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:srcCompat="@drawable/ic_tribe_rounded_rectangle"
            app:layout_constraintTop_toBottomOf="@id/message"
            app:layout_constraintStart_toStartOf="@+id/user_name"
            android:layout_marginTop="@dimen/line_spacing"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tribe_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.Flashat.Label.Small"
            app:layout_constraintTop_toTopOf="@id/tribe_image"
            app:layout_constraintBottom_toBottomOf="@id/tribe_image"
            app:layout_constraintStart_toEndOf="@id/tribe_image"
            app:layout_constraintEnd_toEndOf="@id/message"
            android:textColor="@color/colorPrimary"
            android:layout_marginStart="@dimen/element_spacing"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@{userTribeName}"
            tools:text="DreamerBy BY" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/total_coins_mini"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/element_spacing"
            android:background="@drawable/bg_podium_about_stat_chip"
            android:paddingHorizontal="@dimen/element_spacing"
            android:paddingVertical="@dimen/line_spacing"
            app:goneIfNull="@{stats}"
            app:layout_constraintBottom_toBottomOf="@+id/user_flix_rate"
            app:layout_constraintStart_toEndOf="@+id/user_flix_rate"
            app:layout_constraintTop_toTopOf="@+id/user_flix_rate"
            tools:layout_editor_absoluteX="64dp"
            tools:layout_editor_absoluteY="64dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/total_coins_img_mini"
                android:layout_width="14dp"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/total_coins_count_mini"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_coin" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/total_coins_count_mini"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/line_spacing"
                android:text="@{stats.coinsFormatted}"
                android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                android:textColor="@color/textColorSecondary"
                app:layout_constraintBottom_toBottomOf="@id/total_coins_img_mini"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/total_coins_img_mini"
                app:layout_constraintTop_toTopOf="@id/total_coins_img_mini"
                tools:text="50" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/total_likes_mini"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/element_spacing"
            android:background="@drawable/bg_podium_about_stat_chip"
            android:paddingHorizontal="@dimen/element_spacing"
            android:paddingVertical="@dimen/line_spacing"
            app:goneIfNull="@{stats}"
            app:layout_constraintStart_toEndOf="@+id/total_coins_mini"
            app:layout_constraintTop_toTopOf="@+id/total_coins_mini"
            tools:layout_editor_absoluteX="131dp"
            tools:layout_editor_absoluteY="64dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/total_likes_img_mini"
                android:layout_width="14dp"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/total_likes_count_mini"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_podium_like" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/total_likes_count_mini"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/line_spacing"
                android:text="@{stats.likesFormatted}"
                android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                android:textColor="@color/textColorSecondary"
                app:layout_constraintBottom_toBottomOf="@id/total_likes_img_mini"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/total_likes_img_mini"
                app:layout_constraintTop_toTopOf="@id/total_likes_img_mini"
                tools:text="50" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/user_flix_rate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_podium_transparent_chip"
            android:layout_marginTop="@dimen/element_spacing"
            android:paddingHorizontal="@dimen/element_spacing"
            android:paddingVertical="@dimen/element_spacing"
            android:textAppearance="@style/TextAppearance.Flashat.Label.Tiny.Bold"
            android:textColor="@color/colorPrimary"
            app:goneIfNull="@{stats}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/user_dp"
            app:layout_constraintTop_toBottomOf="@+id/tribe_image"
            tools:text="The President 100%" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>