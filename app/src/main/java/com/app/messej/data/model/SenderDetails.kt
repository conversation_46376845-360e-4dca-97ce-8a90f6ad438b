package com.app.messej.data.model

import androidx.room.TypeConverter
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserRole
import com.app.messej.data.model.enums.UserType
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

data class SenderDetails(
    @SerializedName("id", alternate = ["userId"]            ) override var id           : Int,
    @SerializedName("username"      ) var _username     : String? = "",
    @SerializedName("name"            ) override var name           : String  = "",
    @SerializedName("thumbnail_url", alternate = ["thumbnail"]) override var thumbnail   : String?  = null,
    @SerializedName("deleted_account" ) var deletedAccount : Boolean = false,
    @SerializedName("is_premium"      ) var premium      : Boolean = false,
    @SerializedName("verified"        ) override var verified     : Boolean = false,
    @SerializedName("role"            ) var role           : UserRole?  = null,
//    @SerializedName("country_name"   ) var countryName   : String?  = null,
    @SerializedName("country_code"   ) override var countryCode   : String?  = null,
    @SerializedName("citizenship" , alternate = ["user_citizenship"]  ) override var citizenship   : UserCitizenship?  = null,

    @SerializedName("blocked_by_admin"   ) var blockedByAdminOrEmpowerUser   : Boolean?  = false,
    @SerializedName("blocked_by_leader" ) var blockedByLeader : Boolean? = false,
    @SerializedName("huddle_admin_blocked"   ) var blockedByHuddleAdmin   : Boolean?  = false,
    @SerializedName("user_priority"   ) var priority   : UserRole?  = null,
    @SerializedName("flash_blocked"   ) var blockedFromPostingFlash   : Boolean?  = false,
    @SerializedName("blocked_by_empowered") var blockedByEmpowered   : Boolean?  = false,


    @SerializedName("is_user_live_podium") val userLivePodium: Boolean = false,
    @SerializedName("user_live_podium_id") val userLivePodiumId: String? = null
): AbstractUser() {

    override val username: String
        get() = _username.orEmpty()

    override val membership: UserType
        get() = if (premium) UserType.PREMIUM else UserType.FREE

    val blockedByEitherAdmin: Boolean
        get() = blockedByAdminOrEmpowerUser==true || blockedByLeader==true || blockedByHuddleAdmin == true

    val blockedByEmpoweredUser : Boolean
        get() = blockedByEmpowered == true
    class Converter {
        @TypeConverter
        fun decode(data: String?): SenderDetails? {
            data?: return null
            val type: Type = object : TypeToken<SenderDetails?>() {}.type
            return Gson().fromJson<SenderDetails>(data, type)
        }
        @TypeConverter
        fun encode(someObjects: SenderDetails?): String? {
            return Gson().toJson(someObjects)
        }
    }
    companion object {
        fun from(user: CurrentUser): SenderDetails {
            return SenderDetails(
                id = user.id,
                _username = user.username,
                name = user.profile.name,
                deletedAccount = false,
                premium = user.premium,
                verified = user.verified,
                thumbnail = user.profile.thumbnail,
                countryCode = user.countryCode,
                citizenship = user.citizenship
            )
        }
    }
}