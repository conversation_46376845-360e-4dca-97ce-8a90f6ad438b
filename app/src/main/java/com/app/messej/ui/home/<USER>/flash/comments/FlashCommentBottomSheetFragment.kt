package com.app.messej.ui.home.publictab.flash.comments

import android.annotation.SuppressLint
import android.graphics.Color
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.widget.PopupMenu
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.PostatCommentLikePayload
import com.app.messej.data.model.ReportPackage
import com.app.messej.data.model.api.postat.PostComment
import com.app.messej.data.model.entity.PostCommentItem
import com.app.messej.data.model.entity.PostReply
import com.app.messej.data.model.enums.ReportType
import com.app.messej.databinding.FragmentFlashCommentBottomSheetBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureFlashPostingAllowed
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureInteractionAllowed
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureReportBanAllowed
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureReportContentAllowed
import com.app.messej.ui.utils.ExpandableListBottomSheetDialogFragment
import com.app.messej.ui.utils.FragmentExtensions.confirmPaidLikeAction
import com.app.messej.ui.utils.FragmentExtensions.showInsufficientBalanceAlert
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.TextFormatUtils
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences
import com.bumptech.glide.Glide
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.snackbar.Snackbar

class FlashCommentBottomSheetFragment : ExpandableListBottomSheetDialogFragment(), FlashCommentsAdapter.ChatCommentListener {

    private lateinit var binding: FragmentFlashCommentBottomSheetBinding
    private val args: FlashCommentBottomSheetFragmentArgs by navArgs()
    private val viewModel: FlashCommentsViewModel by viewModels()
    private lateinit var mAdapter: FlashCommentsAdapter


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_FlashCommentsBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_flash_comment_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = true
        setup()
        initAdapter()
        observer()
    }

    private fun initAdapter() {

        binding.messagesList.setContent {
            FlashCommentsRepliesScreen(viewModel = viewModel, listener = object : CommentsListener {
                override fun onReplyClicked(postatComment: PostComment?, reply: PostReply?) {
                    onReplayAction(postatComment,reply)
                }

                override fun onFlashLikeClicked(commentId: String?, replayId: String?) {
                    if(viewModel.isLikeLoading.value==true) return
                    ensureInteractionAllowed {
                        if (viewModel.skipNextTime.value == false) {
                            confirmPaidLikeAction(
                                message = R.string.podium_paid_like_confirmation,
                                skipNextTime = viewModel.skipNextTime.value == true
                            ) { skipSelected ->
                                viewModel.setUpPaidLike(skipSelected)
                                if(replayId!=null) viewModel.sendCommentPaidLike(null, replayId = replayId) else
                                    viewModel.sendCommentPaidLike(commentId = commentId,null)
                            }
                        } else {
                            if(replayId!=null) viewModel.sendCommentPaidLike(null, replayId = replayId)
                            else viewModel.sendCommentPaidLike(commentId = commentId,null)
                        }
                    }
                }

                override fun onPostatLikeClicked(payload: PostatCommentLikePayload) {}


                override fun onReportClicked(data: PostComment) {}

                override fun onDeleteFlashClicked(commentId: String, reply: Boolean?, userId: Int) {
                    if(userId != viewModel.user.id){
                        showToast("You Cant Delete Others message")
                        return
                    }
                    reply?.let { viewModel.validateDeleteAction(it, commentId) }
                }

                override fun onDeletePostatClicked(messageId: String, commentId: String, reply: Boolean?, userId: Int) {}


                override fun onProfileImageClicked(userId: Int) {
                    findNavController().navigateSafe(FlashCommentBottomSheetFragmentDirections.actionGlobalPublicUserProfileFragment(userId))
                }

            })
        }
    }

    private fun observer() {

        viewModel.onCommentAdded.observe(viewLifecycleOwner) {
            binding.layoutReply.visibility = View.GONE
        }
        viewModel.skipNextTime.observe(viewLifecycleOwner) {
            Log.w("SKipNxtTime", "observe: skipPaidLikeConfirmation: ${it}")
        }
        viewModel.isLikeLoading.observe(viewLifecycleOwner) {
            Log.d("LIKE_ENABLED", "observe: ${it}")
        }
        viewModel.onPaidLikeInsufficientBalance.observe(viewLifecycleOwner) {
            if (it) {
                showInsufficientBalanceAlert(R.string.podium_paid_like_balance_error)
            }
        }
        viewModel.onCommentDeleted.observe(viewLifecycleOwner){
            Log.d("DELETE_COMMENT", "visibility: $it")
             binding.undoCommentContainer.visibility = if(it) View.VISIBLE else View.GONE
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setup() {
        viewModel.setFlashId(args.flashId, args.commentsEnabled)
        viewModel.getSkipAction()
        if (!args.commentsEnabled) {
            showToast(R.string.flash_comment_disabled_taost, Snackbar.LENGTH_INDEFINITE)
        }
        binding.chatSendButton.setOnClickListener {
            ensureFlashPostingAllowed {
                viewModel.writeComment()
            }
        }
        snapToBottom(binding.messageContainer, binding.root)

        binding.chatInput.editText?.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                (dialog as? BottomSheetDialog)?.behavior?.apply {
                    state = BottomSheetBehavior.STATE_EXPANDED
                }
            }
        }
//        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
//        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
//        emptyViewBinding.prepare(
//            message = R.string.chat_eds_comments
//        )

        binding.btnClose.setOnClickListener {
            binding.layoutReply.visibility = View.GONE
            binding.inputComment.setText("")
            viewModel.postMentionedUser(null)
        }
        binding.textUndo.setOnClickListener {
            viewModel.undoComment()
        }
        binding.inputComment.setOnTouchListener { view, event ->
            view.parent.requestDisallowInterceptTouchEvent(true)
            false
        }
    }

    override fun onCommentOptionsClick(msg: PostCommentItem, position: Int, view: View) {
        val popup = PopupMenu(requireContext(), view)
        popup.menuInflater.inflate(R.menu.menu_flash_comment_actions, popup.menu)
        popup.setForceShowIcon(true)
        val userIsPremium = viewModel.user.premium
        val iAmFlashOwner = viewModel.user.id == args.flashOwnerId
        val isOwnComment = msg.senderId == viewModel.user.id
        val isSenderDeleted = msg.senderDetails?.deletedAccount == true

        popup.menu.apply {
            findItem(R.id.action_delete).isVisible = isOwnComment || iAmFlashOwner
            findItem(R.id.action_report).isVisible = false //!isOwnComment && msg.isReported!=true && viewModel.user.canReport(msg.senderDetails)
            findItem(R.id.action_report_and_hide).isVisible = false // !isOwnComment && viewModel.user.canReportAndHide(msg.senderDetails)
            findItem(R.id.action_ban).isVisible = false // !isOwnComment && viewModel.user.canBan(msg.senderDetails)
        }

        popup.setOnMenuItemClickListener { menuItem ->
            when (menuItem.itemId) {
                R.id.action_delete -> viewModel.deleteFlashComment(msg.commentId)
                R.id.action_report -> {
                    ensureReportContentAllowed {
//                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.FlashComment(msg, reportType = ReportType.REPORT).serialize()))
                    }
                }

                R.id.action_report_and_hide -> {
                    ensureReportContentAllowed {
//                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.FlashComment(msg, reportType = ReportType.REPORT_AND_HIDE).serialize()))
                    }
                }

                R.id.action_ban -> ensureReportBanAllowed {
                    msg.senderDetails?.asBasicUser()?.let {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.User(it, reportType = ReportType.BAN).serialize()))
                    }
                }

                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }

        popup.show()
    }

    override fun onClickUser(msg: PostCommentItem) {
        findNavController().navigateSafe(FlashCommentBottomSheetFragmentDirections.actionGlobalPublicUserProfileFragment(msg.senderId))
    }

    override fun onResume() {
        super.onResume()
//        mAdapter.refresh()
    }

    fun onReplayAction(postatComment: PostComment?, reply: PostReply?) {
        val comment = reply ?: postatComment
        val sender = comment?.senderDetails
        val commentId = if (reply != null) reply.messageId else comment?.commentId

        viewModel.postMentionedUser(sender)
        commentId?.let { viewModel.setCommentId(it) }

        binding.apply {
            layoutReply.visibility = View.VISIBLE
            badgePremium.isVisible = sender?.premium == true
           if(reply!=null){
               val result = comment?.comment?.replace(Regex("#[^#]+#"), "")?.trim()?:""
               replyingText.text =resources.getString(R.string.replying_to, sender?.name ?: "", result)
            }else {
               replyingText.text =    resources.getString(R.string.replying_to, sender?.name, comment?.comment)}

            Glide.with(requireContext()).load(sender?.thumbnail).placeholder(R.drawable.im_user_placeholder_opaque).into(imagePersonPhoto)
        }

        binding.inputComment.apply {
            val str = resources.getString(R.string.chat_replay_item, sender?.name)
            Log.d("MentionedUser", "disply text ${str}")
            val mentionedUser = str.highlightOccurrences(str) {
                ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.colorPrimaryDark))
            }
            setText(mentionedUser)
            setSelection(mentionedUser.length)
            Log.d("MentionedUser", "${mentionedUser}")
            viewModel.setMentionedUser(mentionedUser.toString())
        }

    }

}