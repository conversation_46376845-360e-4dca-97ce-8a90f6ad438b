package com.app.messej.ui.home.notification

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.appcompat.widget.PopupMenu
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.profile.BirthdayUser
import com.app.messej.data.model.entity.Notification
import com.app.messej.data.model.entity.Notification.NotificationAction.ADMIN_ANNOUNCEMENT
import com.app.messej.data.model.entity.Notification.NotificationAction.BIRTHDAYS
import com.app.messej.data.model.entity.Notification.NotificationAction.CASE_NOTIFICATION
import com.app.messej.data.model.entity.Notification.NotificationAction.CASE_NOTIFICATION_NO_FINE
import com.app.messej.data.model.entity.Notification.NotificationAction.CITIZENSHIP_LEVEL_UPGRADE
import com.app.messej.data.model.entity.Notification.NotificationAction.COINS_UPDATE
import com.app.messej.data.model.entity.Notification.NotificationAction.DEALS
import com.app.messej.data.model.entity.Notification.NotificationAction.E_TRIBE
import com.app.messej.data.model.entity.Notification.NotificationAction.REPORTED_PODIUM_WINNER
import com.app.messej.data.model.entity.Notification.NotificationAction.FLASH_COMMENT
import com.app.messej.data.model.entity.Notification.NotificationAction.FLIX_PURCHASED
import com.app.messej.data.model.entity.Notification.NotificationAction.GIFT_RECEIVE
import com.app.messej.data.model.entity.Notification.NotificationAction.HUDDLE_MESSAGE
import com.app.messej.data.model.entity.Notification.NotificationAction.INVITE_PARTICIPANT
import com.app.messej.data.model.entity.Notification.NotificationAction.INVITE_PODIUM
import com.app.messej.data.model.entity.Notification.NotificationAction.JURY_CASE_REPORT_CLOSED
import com.app.messej.data.model.entity.Notification.NotificationAction.JURY_REPORT_FINE_CLOSED
import com.app.messej.data.model.entity.Notification.NotificationAction.PODIUM
import com.app.messej.data.model.entity.Notification.NotificationAction.PODIUM_EXTERNAL_CONTRIBUTE_CHALLENGE
import com.app.messej.data.model.entity.Notification.NotificationAction.PODIUM_GIFT
import com.app.messej.data.model.entity.Notification.NotificationAction.POLL_INVITE
import com.app.messej.data.model.entity.Notification.NotificationAction.POLL_VOTED
import com.app.messej.data.model.entity.Notification.NotificationAction.PRESIDENT
import com.app.messej.data.model.entity.Notification.NotificationAction.RENEW_SUBSCRIPTION
import com.app.messej.data.model.entity.Notification.NotificationAction.RENEW_SUBSCRIPTION_REMINDER
import com.app.messej.data.model.entity.Notification.NotificationAction.REPORT_ADVOCATE_FEE
import com.app.messej.data.model.entity.Notification.NotificationAction.REPORT_BAN_FINE_DISTRIBUTION
import com.app.messej.data.model.entity.Notification.NotificationAction.REPORT_COMMENT
import com.app.messej.data.model.entity.Notification.NotificationAction.REPORT_FINE_CLOSED
import com.app.messej.data.model.entity.Notification.NotificationAction.REPORT_FINE_DISTRIBUTION
import com.app.messej.data.model.entity.Notification.NotificationAction.REPORT_FINE_INVESTIGATION_BUREAU
import com.app.messej.data.model.entity.Notification.NotificationAction.REPORT_POST
import com.app.messej.data.model.entity.Notification.NotificationAction.SELL_FLIX_PAYOUTS
import com.app.messej.data.model.entity.Notification.NotificationAction.SUGGEST_USERNAME
import com.app.messej.data.model.entity.Notification.NotificationAction.USER_LEVEL_UPGRADE
import com.app.messej.data.model.enums.AdvocatesUnionFilter
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.LegalAffairTabs
import com.app.messej.data.model.enums.LegalAffairsMainTab
import com.app.messej.data.model.enums.LegalAffairsViolationSubTab
import com.app.messej.data.model.enums.PurchaseItem
import com.app.messej.data.model.enums.ReportedTab
import com.app.messej.data.model.enums.TransactionTab
import com.app.messej.data.model.enums.UserType
import com.app.messej.data.model.socket.SentGiftPayload
import com.app.messej.data.utils.JsonUtil.fromJson
import com.app.messej.databinding.FragmentNotificationBinding
import com.app.messej.ui.home.CommonChallengeViewModel
import com.app.messej.ui.home.businesstab.HomeBusinessFragment
import com.app.messej.ui.home.promobar.CommonPromoBarUtil.showAdminAnnouncementDialog
import com.app.messej.ui.home.publictab.HomePublicFragment
import com.app.messej.ui.home.publictab.myETribe.showETribeSupperStarAlert
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.showUserJoinByRatingError
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.downloadAndShowBirthdayResultVideo
import com.app.messej.ui.utils.FragmentExtensions.downloadAndShowGift
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.google.android.material.button.MaterialButton
import com.google.gson.Gson
import com.kennyc.view.MultiStateView
import java.time.MonthDay


class NotificationFragment : Fragment() {

    private val viewModel: NotificationViewModel by viewModels()
    private lateinit var binding: FragmentNotificationBinding
    private var mAdapter: NotificationAdapter? = null
    private val challengeViewModel: CommonChallengeViewModel by activityViewModels()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_notification, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.common_notifications)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
        viewModel.updateUnreadNotificationCount()
    }

    override fun onResume() {
        super.onResume()
        viewModel.updateUnreadNotificationCount()
    }

    private fun showMoreMenu(v: View, item: Notification) {
        val popup = PopupMenu(requireContext(), v)
        popup.menuInflater.inflate(R.menu.menu_notification_actions, popup.menu)
        popup.setForceShowIcon(true)
        popup.setOnMenuItemClickListener { menuItem ->
            when(menuItem.itemId) {
                R.id.action_delete -> viewModel.deleteNotification(item.id)
                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
    }

    private fun observe() {
        viewModel.notificationList.observe(viewLifecycleOwner) { pagingData ->
            mAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
        }

        viewModel.apiResponse.observe(viewLifecycleOwner) {
            it?.let {
                Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun setup() {
        initAdapter()
        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatImageView>(R.id.eds_empty_image).apply { setImageResource(R.drawable.bg_no_notification) }
            findViewById<AppCompatTextView>(R.id.eds_empty_message).apply {
                text = resources.getString(R.string.title_empty_notification_message)
                setTextColor(ContextCompat.getColor(context, R.color.black))
            }
            findViewById<MaterialButton>(R.id.eds_empty_action).apply {
                visibility = View.GONE
            }
        }
    }

    private fun initAdapter() {
        mAdapter = NotificationAdapter(object : NotificationAdapter.ActionListener {

            override fun showMoreAction(item: Notification): Boolean {
                return true
            }

            override fun onMoreActionClick(item: Notification, view: View) {
                showMoreMenu(view, item)
            }

            override fun getPositiveAction(item: Notification): String? {
                return when(item.action) {
                    SUGGEST_USERNAME -> if(item.status==null) resources.getString(R.string.common_accept) else null
                    RENEW_SUBSCRIPTION_REMINDER -> resources.getString(R.string.notification_action_renew)
                    RENEW_SUBSCRIPTION -> null
                    else -> null
                }
            }

            override fun onPositiveActionClick(item: Notification) {
                when(item.action) {
                    SUGGEST_USERNAME -> viewModel.updateNotification(true, item.associateObjId ?: 0, item.id)
                    RENEW_SUBSCRIPTION_REMINDER-> findNavController().navigateSafe(NotificationFragmentDirections.actionGlobalUpgradePremiumFragment())
                    else -> {}
                }
            }

            override fun getNegativeAction(item: Notification): String? {
                return when(item.action) {
                    SUGGEST_USERNAME -> if(item.status==null) resources.getString(R.string.common_decline) else null
                    else -> null
                }
            }

            override fun onNegativeActionClick(item: Notification) {
                when(item.action) {
                    SUGGEST_USERNAME -> viewModel.updateNotification(false, item.associateObjId ?: 0, item.id)
                    else -> {}
                }
            }

            override fun getActionStatus(item: Notification): String? {
                return when (item.action) {
                    SUGGEST_USERNAME -> when (item.status) {
                        Notification.NotificationStatus.DECLINED -> resources.getString(R.string.notification_status_accepted)
                        Notification.NotificationStatus.ACCEPTED -> resources.getString(R.string.notification_status_declined)
                        else -> null
                    }
                    else -> null
                }
            }

            override fun onItemClick(item: Notification, position: Int) {
                if (item.readTime == null) {
                    viewModel.readNotification(item.id)
                }

                Log.d("ActionNameNotification",item.toString())
                when (item.action) {

                    CASE_NOTIFICATION_NO_FINE -> {
                        //Navigate to my legal records
                        findNavController().navigateSafe(
                            NotificationFragmentDirections.actionGlobalLegalAffairsFragment()
                        )
                    }

                    REPORT_FINE_DISTRIBUTION -> {
                        findNavController().navigateSafe(
                            NotificationFragmentDirections.actionGlobalLegalAffairsFragment(
                                defaultMyLegalRecordTab = LegalAffairTabs.Reporting
                            )
                        )
                    }

                    REPORT_BAN_FINE_DISTRIBUTION -> {
                        findNavController().navigateSafe(
                            NotificationFragmentDirections.actionGlobalLegalAffairsFragment(
                                defaultMyLegalRecordTab = LegalAffairTabs.Bans
                            )
                        )
                    }

                    REPORT_ADVOCATE_FEE -> {
                        // Navigate to Advocates union defend screen
                        findNavController().navigateSafe(
                            NotificationFragmentDirections.actionGlobalLegalAffairsFragment(
                                defaultMainTab = LegalAffairsMainTab.AdvocatesUnion,
                                defaultAdvocateFilter = AdvocatesUnionFilter.Defended
                            )
                        )
                    }

                    REPORT_FINE_CLOSED -> {
                        // Navigate to my legal records closed tab
                        findNavController().navigateSafe(
                            NotificationFragmentDirections.actionGlobalLegalAffairsFragment(defaultViolationSubTab = LegalAffairsViolationSubTab.Closed)
                        )
                    }

                    REPORT_FINE_INVESTIGATION_BUREAU -> {
                        //Navigate to investigation bureau
                        findNavController().navigateSafe(
                            NotificationFragmentDirections.actionGlobalLegalAffairsFragment(defaultMainTab = LegalAffairsMainTab.InvestigationBureau)
                        )
                    }

                    JURY_CASE_REPORT_CLOSED, JURY_REPORT_FINE_CLOSED -> {
                        //Navigate to Jury screen closed
                        findNavController().navigateSafe(
                            NotificationFragmentDirections.actionGlobalLegalAffairsFragment(
                                defaultMainTab = LegalAffairsMainTab.Jury
                            )
                        )
                    }

                    CASE_NOTIFICATION -> {
                        // Showing all fines.
                        item.associateObjId?.let {
                            findNavController().navigateSafe(
                                NotificationFragmentDirections.actionGlobalPayFineFragment(fineCategory = null)
                            )
                        }
                    }

                    INVITE_PARTICIPANT,POLL_INVITE,POLL_VOTED,HUDDLE_MESSAGE -> {
                        item.associateObjId?.let {
                            if(item.isPrivate == true) {
                                findNavController().navigateSafe(NotificationFragmentDirections.actionGlobalNavigationChatGroup(it))
                            }else{
                                findNavController().navigateSafe(NotificationFragmentDirections.actionGlobalNavChatHuddle(it))
                            }
                        }
                    }
                    RENEW_SUBSCRIPTION ->{
                        if(viewModel.user.membership==UserType.PREMIUM ) {
                            findNavController().navigateSafe(NotificationFragmentDirections.actionGlobalAlreadySubscribedFragment(false))
                        }
                    }
                    GIFT_RECEIVE -> {
                    Gson().fromJson<SentGiftPayload>(item.assosiateData?.otherData.toString()).let { otherData ->
                        if (otherData != null) {
                            if (otherData.hasVideo) {
                                downloadAndShowGift(otherData.copy(receiverId = viewModel.user.id))
                            } else {
                                otherData.senderId?.let {senderId->
                                    findNavController().navigateSafe(
                                        NotificationFragmentDirections.actionGlobalNotificationLottieBottomSheetFragment(
                                            otherData.id, senderId, true
                                        )
                                    )
                                }

                            }
                        }else{
                            findNavController().navigateSafe(NotificationFragmentDirections.actionGlobalGiftFileFragment())
                        }
                    }
                    }
                    BIRTHDAYS->{
                        Gson().fromJson<BirthdayUser>(item.assosiateData?.otherData.toString()).let { otherData ->
                            if (otherData != null) {
                                Log.w("BIRTHDAY", "lastBirthdayDateNoti" + MonthDay.from(otherData.parsedDateOfBirth).equals(MonthDay.now()))

                                if (MonthDay.from(otherData.parsedDateOfBirth).equals(MonthDay.now())) {
                                    if (otherData.hasVideo) {
                                        downloadAndShowBirthdayResultVideo(otherData.birthdayAnimationUrlAndroid!!)
                                    } else {
                                        otherData.userId?.let {
                                            findNavController().navigateSafe(NotificationFragmentDirections.actionGlobalBirthdayNotificationBottomSheetFragment(userId = it, currentBirthday = true))
                                        }
                                    }
                                } else {
                                    if(!otherData.hasVideo){
                                        otherData.userId?.let {
                                            findNavController().navigateSafe(NotificationFragmentDirections.actionGlobalBirthdayNotificationBottomSheetFragment(userId = it, currentBirthday = false))
                                        }
                                    } else {
                                        Log.w("BIRTHDAY", "lastBirthdayDateNoti" + MonthDay.from(otherData.parsedDateOfBirth).equals(MonthDay.now()))
                                    }

                                }
                            }else{
                                Log.w("BIRTHDAY", "other Data is empty")
                            }
                        }
                    }

                    FLASH_COMMENT->{
                        item.assosiateData?.flashId?.let { flashId ->
                            findNavController().navigateSafe(NotificationFragmentDirections.actionGlobalFlashSinglePlayerFragment(flashId,item.assosiateData.commentId))
                        }
                    }

                    DEALS->{
                        findNavController().navigateSafe(NotificationFragmentDirections.actionGlobalHomeBusinessFragment(destination = HomeBusinessFragment.TAB_DEALS))
                    }

                    Notification.NotificationAction.FORCEFUL_USER_NAME_CHANGE -> {
                        findNavController().navigateSafe(NotificationFragmentDirections.actionGlobalProfileFragment())
                    }
                    Notification.NotificationAction.USER_PROFILE_EDITED -> {
                        findNavController().navigateSafe(NotificationFragmentDirections.actionGlobalProfileFragment())

                    }
                    Notification.NotificationAction.SUBSCRIPTION_RENEW_FAILURE -> {
                        findNavController().navigateSafe(NotificationFragmentDirections.actionGlobalUpgradePremiumFragment())
                    }
                    Notification.NotificationAction.SUBSCRIPTION_FAILURE -> {
                        findNavController().navigateSafe(NotificationFragmentDirections.actionGlobalUpgradePremiumFragment())
                    }
                    Notification.NotificationAction.ADMIN_INVITE ->
                    {
                        item.associateObjId?.let {
                            if (item.isPrivate == true) {
                                findNavController().navigateSafe(NotificationFragmentDirections.actionGlobalNavigationChatGroup(it))
                            } else {
                                findNavController().navigateSafe(NotificationFragmentDirections.actionGlobalNavChatHuddle(it))
                            }
                        }
                    }
                    Notification.NotificationAction.ACCEPT_JOIN_REQUEST -> {
                        item.associateObjId?.let {
                            if (item.isPrivate == true) {
                                findNavController().navigateSafe(NotificationFragmentDirections.actionGlobalNavigationChatGroup(it))
                            } else {
                                findNavController().navigateSafe(NotificationFragmentDirections.actionGlobalNavChatHuddle(it))
                            }
                        }
                    }
                    INVITE_PODIUM,PODIUM->{
                        val requiredUserRating = item.assosiateData?.requiredUserRating ?: 0
                        if (requiredUserRating > viewModel.user.userRatingPercent.toInt()) {
                            val message = if (item.assosiateData?.requiredUserRating == 100 && viewModel.user.userRatingPercent.toInt() != 100)
                                getString(R.string.podium_join_with_rating_with_hundred)
                            else getString(R.string.podium_join_with_rating_ninety_or_above)

                            showUserJoinByRatingError(
                                message = message,
                                userRating = viewModel.user.userRatingPercent,
                                isPremium = viewModel.user.premium
                            )
                            return
                        }

                        if(viewModel.user.premium) {
                            findNavController().navigateSafe(NotificationFragmentDirections.actionGlobalHomePublicFragment(HomePublicFragment.PublicTab.PODIUM.ordinal))
                        }else{
                            findNavController().navigateSafe(NotificationFragmentDirections.actionGlobalPublicPodiumStandaloneFragment())
                        }
                    }
                    PODIUM_EXTERNAL_CONTRIBUTE_CHALLENGE -> {
                    Log.d("timeINV",""+item.invitedTimeRemaining)
                        Log.d("PIDDD",""+item.assosiateData?.podiumId)
                        if(item.invitedTimeRemaining.toInt() ==0) {showToast(R.string.podium_external_contributor_timeout,Toast.LENGTH_SHORT)}
                         else{item.assosiateData?.podiumId?.let { checkAndInitExternalContributorAlert(it) }}
                    }
                    REPORT_COMMENT->{
                        val type = item.huddleType?: HuddleType.PRIVATE
                        val huddleId = item.associateObjId ?: 0
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalHuddleReportBaseFragment(huddleId, type,ReportedTab.TAB_COMMENTS))
                    }

                    REPORT_POST->{
                        val type = item.huddleType?: HuddleType.PRIVATE
                        val huddleId = item.associateObjId ?: 0
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalHuddleReportBaseFragment(huddleId, type,ReportedTab.TAB_MESSAGES))
                    }
                    SELL_FLIX_PAYOUTS->{
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalToPayoutHistory())
                    }
                    PODIUM_GIFT->{ findNavController().navigateSafe(NotificationFragmentDirections.actionGlobalGiftFileFragment())}
                    PRESIDENT->{
                        item.assosiateData?.currentPresidentId?.let { currentPresidentId->
//                            val response =UserBirthdayResponse(animationForPresident = data.animationForPresident, currentPresidentName = data.currentPresidentName, currentPresidentId = data.currentPresidentId, isCurrentUserBday = false,)
//                            setPresidentNotification(response)
                            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalLevelUpgradationBottomSheetFragment(currentPresidentId, false))
                        }
                        }

                    USER_LEVEL_UPGRADE ->{
                        item.assosiateData?.let {
                            findNavController().navigateSafe(
                                NavGraphHomeDirections.actionGlobalLevelUpgradationBottomSheetFragment(
                                    viewModel.user.id, true
                                )
                            )
                        }
                  }
                    CITIZENSHIP_LEVEL_UPGRADE ->{
                        findNavController().navigateSafe(
                                NavGraphHomeDirections.actionGlobalLevelUpgradationBottomSheetFragment(
                                    item.senderId?:return, false
                                )
                        )
                    }
                    FLIX_PURCHASED->{
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalFlixPurchaseHistoryFragment(PurchaseItem.BUY_FLIX))
                    }
                    COINS_UPDATE->{
                        val action = NotificationFragmentDirections.actionGlobalTransactionList(TransactionTab.TAB_COINS)
                        findNavController().navigateSafe(action)
                    }
                    ADMIN_ANNOUNCEMENT -> {
                        showAdminAnnouncementDialog(context = requireContext(), message = item.body)
                    }
                    E_TRIBE -> {
                        showETribeSupperStarAlert(
                            superStarName = item.assosiateData?.superStarName,
                            message = item.assosiateData?.content ?: "",
                            context = requireContext()
                        )
                    }
                    REPORTED_PODIUM_WINNER -> {
                        findNavController().navigateSafe(
                            direction = NotificationFragmentDirections.actionGlobalGiftFileFragment()
                        )
                    }
                    else ->{

                    }
                }
            }
        })
        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)
            }
        }
        val layoutMan = LinearLayoutManager(context)
        binding.notificationList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

    }

    private fun checkAndInitExternalContributorAlert(podiumId:String) {
        if (podiumId.isEmpty()) return
        challengeViewModel.loadContributorRequest(podiumId)
    }
}