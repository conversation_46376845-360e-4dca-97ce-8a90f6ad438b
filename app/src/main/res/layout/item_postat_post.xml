<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="postat"
            type="com.app.messej.data.model.entity.Postat" />
        <variable name="isSelf" type="Boolean" />
        <import type="com.app.messej.data.model.enums.UserCitizenship"/>
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cslPost"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/extra_margin">

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/ivUserDp"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_margin="@dimen/element_spacing"
            android:elevation="2dp"
            android:scaleType="centerCrop"
            app:imageUrl="@{postat.senderDetails.thumbnail}"
            app:placeholder="@{@drawable/im_user_placeholder}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:riv_corner_radius="@dimen/message_list_dp_size"
            tools:src="@drawable/im_user_placeholder" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/live_podium_indicator"
            android:layout_width="10dp"
            android:layout_height="10dp"
            app:srcCompat="@drawable/ic_dot"
            android:tint="@color/colorPass"
            android:elevation="4dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@id/ivUserDp"
            app:visibleIf="@{postat.senderDetails.userLivePodium &amp;&amp; !isSelf}"
            app:layout_constraintBottom_toBottomOf="@id/ivUserDp"
            tools:visibility="visible" />


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/superstar_premium_badge"
            android:layout_width="@dimen/premium_badge_size"
            android:layout_height="@dimen/premium_badge_size"
            android:elevation="4dp"
            android:layout_margin="-1dp"
            app:userBadge="@{postat.senderDetails.userBadge}"
            app:layout_constraintStart_toStartOf="@id/ivUserDp"
            app:layout_constraintTop_toTopOf="@id/ivUserDp"
            tools:src="@drawable/ic_user_badge_premium" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/sender_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/activity_margin"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintBottom_toTopOf="@id/music_info"
            app:layout_constraintStart_toEndOf="@+id/ivUserDp"
            app:layout_constraintTop_toTopOf="@+id/ivUserDp"
            app:layout_constraintEnd_toStartOf="@+id/btnMore">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/commentor_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{postat.senderDetails.name}"
                tools:text="Mohammed Shahim"
                android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                app:layout_constrainedWidth="true"
                android:ellipsize="end"
                android:maxLines="1"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toStartOf="@id/comment_flag"/>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/comment_flag"
                android:layout_width="wrap_content"
                android:layout_height="12dp"
                android:layout_marginStart="@dimen/element_spacing"
                android:adjustViewBounds="true"
                app:layout_constraintBottom_toBottomOf="@+id/commentor_name"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/commentor_name"
                app:layout_constraintTop_toTopOf="@+id/commentor_name"
                app:srcCompat="@drawable/bg_add_flash_button"
                tools:srcCompat="@drawable/flag_france" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/music_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible"
            app:goneIfNot="@{postat.musicData.valid==true}"
            app:layout_constraintStart_toStartOf="@id/sender_info"
            app:layout_constraintEnd_toEndOf="@id/sender_info"
            app:layout_constraintTop_toBottomOf="@id/sender_info"
            app:layout_constraintBottom_toBottomOf="@id/ivUserDp">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/music_ic"
                android:layout_width="18dp"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_postat_music"
                app:tint="@color/textColorSecondary" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/music_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Flashat.Label.Small.Bold"
                android:textColor="@color/textColorSecondary"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constrainedWidth="true"
                android:maxLines="1"
                android:ellipsize="end"
                android:text="@{postat.musicData.mediaName}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/music_ic"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Space Oddity" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/music_dot"
                android:layout_width="3dp"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:layout_marginStart="@dimen/line_spacing"
                app:goneIfNullOrBlank="@{postat.musicData.singersAsString}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toEndOf="@id/music_title"
                app:srcCompat="@drawable/ic_dot"
                app:tint="@color/textColorSecondary" />

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/line_spacing"
                android:textAppearance="@style/TextAppearance.Flashat.Label.Small"
                android:textColor="@color/textColorSecondary"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constrainedWidth="true"
                app:goneIfNullOrBlank="@{postat.musicData.singersAsString}"
                android:text="@{postat.musicData.singersAsString}"
                android:maxLines="1"
                android:ellipsize="end"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/music_dot"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="David Bowie" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnMore"
            style="@style/Widget.Flashat.Button.TextButton.IconOnly"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:icon="@drawable/ic_more_vertical"
            app:layout_constraintBottom_toBottomOf="@id/ivUserDp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/ivUserDp" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/media_holder"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintDimensionRatio="1"
            android:layout_marginTop="@dimen/element_spacing"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ivUserDp">

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/mediaViewPager"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.tbuonomo.viewpagerdotsindicator.DotsIndicator
            android:id="@+id/dots_indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/line_spacing"
            app:dotsColor="@color/colorAlwaysLightSurfaceSecondaryDark"
            app:dotsCornerRadius="8dp"
            app:dotsSize="5dp"
            app:dotsSpacing="4dp"
            app:goneIfNot="@{postat.mediaCount>1}"
            app:dotsWidthFactor="5"
            app:selectedDotColor="@color/colorAlwaysLightSurfaceSecondaryDark"
            app:progressMode="true"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/media_holder"
            />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/footer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/dots_indicator"
            app:layout_constraintEnd_toEndOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/gift_holder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                goneIf="@{postat.senderDetails.citizenship==UserCitizenship.GOLDEN}"
                app:layout_constraintHorizontal_bias="0.0"
                android:paddingHorizontal="@dimen/activity_margin"
                android:paddingVertical="@dimen/element_spacing"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/comment_count_holder">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivGift"
                    android:layout_width="20dp"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_gift"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:tint="@color/colorPrimary" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvGiftCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:text="@{postat.giftCountFormatted}"
                    android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                    android:textColor="@color/colorPrimary"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/ivGift"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="26" />

                <View
                    android:id="@+id/viewSeparation"
                    android:layout_width="1dp"
                    android:layout_height="0dp"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:layout_marginVertical="2dp"
                    android:background="@color/colorPrimary"
                    app:layout_constraintStart_toEndOf="@id/tvGiftCount"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvGiftCountAfter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/element_spacing"
                    android:text="@{postat.giftValueFormatted}"
                    android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                    android:textColor="@color/colorPrimary"
                    app:layout_constraintStart_toEndOf="@id/viewSeparation"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="500" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/comment_count_holder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:goneIf="@{postat.turnOffComments}"
                tools:visibility="visible"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                android:paddingHorizontal="@dimen/activity_margin"
                android:paddingVertical="@dimen/element_spacing"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/comment_ic"
                    android:layout_width="16dp"
                    android:layout_height="wrap_content"
                    android:adjustViewBounds="true"
                    app:srcCompat="@drawable/ic_postat_comments"
                    android:tint="@color/textColorSecondaryLight"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/comment_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{postat.totalCommentsFormatted}"
                    android:layout_marginStart="@dimen/line_spacing"
                    android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                    android:textColor="@color/textColorSecondaryLight"
                    tools:text="20"
                    app:layout_constraintStart_toEndOf="@id/comment_ic"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/post_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Flashat.Label"
                android:paddingHorizontal="@dimen/activity_margin"
                android:textColor="@color/textColorSecondaryLight"
                app:layout_constraintHorizontal_bias="1.0"
                app:layout_constrainedWidth="true"
                android:maxLines="1"
                android:ellipsize="end"
                app:layout_constraintBottom_toBottomOf="@id/comment_count_holder"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/comment_count_holder"
                app:layout_constraintTop_toTopOf="@id/comment_count_holder"
                tools:text="3 Hours ago" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.webtoonscorp.android.readmore.ReadMoreTextView
            android:id="@+id/tvComment"
            style="Widget.Flashat.Postat.Message"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/activity_margin"
            android:layout_marginTop="@dimen/line_spacing"
            android:layout_marginEnd="@dimen/activity_margin"
            android:text="@{postat.message}"
            app:goneIfNullOrBlank="@{postat.message}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/footer"
            app:readMoreText="@string/postat_read_more"
            app:readMoreTextColor="@color/textColorSecondaryLight"
            app:readMoreTextSize="13sp"
            tools:text="Post Description" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
