package com.app.messej.ui.home.privatetab.messages

import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import androidx.appcompat.view.ActionMode
import androidx.appcompat.widget.PopupMenu
import androidx.core.view.MenuProvider
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.ViewModelProvider
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.ConnectivityObserver
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.entity.PrivateChat
import com.app.messej.data.model.entity.PrivateChat.PrivateMessageTabType
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.databinding.LayoutPrivateMessagesBinding
import com.app.messej.ui.home.CommonHomeViewModel
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.ViewUtils
import com.kennyc.view.MultiStateView


abstract class  PrivateMessagesBaseFragment : Fragment(), MenuProvider {

    protected lateinit var tab: PrivateMessageTabType

    protected abstract var binding: LayoutPrivateMessagesBinding

    protected var mAdapter: PrivateMessagesAdapter? = null

    protected lateinit var viewModel: PrivateMessagesViewModel

    private val commonHomeViewModel: CommonHomeViewModel by activityViewModels()

    companion object {
        const val ARG_TAB = "chatTab"

        fun getTabBundle(tab: PrivateMessageTabType) = Bundle().apply {
            putInt(ARG_TAB,tab.ordinal)
        }

        fun parseTabBundle(bundle: Bundle?): PrivateMessageTabType {
            val tabInt = bundle?.getInt(ARG_TAB)?:0
            return PrivateMessageTabType.values()[tabInt]
        }
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel = ViewModelProvider(this)[tab.toString(),PrivateMessagesViewModel::class.java]
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        viewModel.setTabType(tab)
        setup()
        observe()
    }

    private fun setup() {
        initAdapter()
        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        emptyViewBinding.prepare(
            image = if (tab == PrivateMessageTabType.INTRUDER) R.drawable.im_eds_intruders else R.drawable.im_eds_chats,
            message = if (tab == PrivateMessageTabType.INTRUDER) R.string.private_intruder_eds else R.string.private_messages_eds,
            action = if (tab == PrivateMessageTabType.INTRUDER) null else R.string.private_messages_eds_action
        ) {
            navigateToMessageSearch()
        }
        binding.newMessageFab.setOnClickListener {
            navigateToMessageSearch()
        }
    }

    override fun onResume() {
        super.onResume()
        mAdapter?.refresh()
    }

    private fun observe() {
        viewModel.chatList.observe(viewLifecycleOwner) {
            it?: return@observe
            mAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }

        viewModel.onNavigateToPrivateMessage.observe(viewLifecycleOwner) {
            navigateToChat(it.first,it.second)
        }

        viewModel.chatListSelectionMode.observe(viewLifecycleOwner){
            showSelectionMode(it)
        }

        commonHomeViewModel.connectionStatus.observe(viewLifecycleOwner){
            if (it == ConnectivityObserver.ConnectionStatus.AVAILABLE) {
                mAdapter?.refresh()
            }
        }
    }

    override fun onPause() {
        super.onPause()
        if(activity?.isChangingConfigurations != true) {
            Log.d("PMBF", "onPause: hide selection mode")
            viewModel.exitSelectionMode()
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        Log.d("OCM", "onCreateMenu: PrivateMessagesBaseFragment")
        return menuInflater.inflate(R.menu.menu_home_private_messages,menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_search -> navigateToMessageSearch()
            R.id.action_more -> showMoreMenu(menuItem)
            else -> return false
        }
        return true
    }

    private fun showMoreMenu(v: MenuItem) {
        val view = activity?.findViewById<View>(v.itemId)?: return
        val popup = PopupMenu(requireContext(),view)
        popup.menuInflater.inflate(R.menu.menu_home_private_messages_more, popup.menu)
        popup.setForceShowIcon(true)
        popup.menu.apply {
            findItem(R.id.action_privacy).isVisible = viewModel.user.premium
        }
        popup.setOnMenuItemClickListener { menuItem ->
            when(menuItem.itemId) {
                R.id.action_blocked -> navigateToBlockedMessages()
                R.id.action_pending -> navigateToPendingList()
                R.id.action_invited -> navigateToMessageSearch()
                R.id.action_privacy -> navigateToPrivacy()
                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
    }


    var actionMode: ActionMode? = null
    private fun showSelectionMode(show: Boolean) {
        if(show) {
            val callback = object: ActionMode.Callback {
                override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    requireActivity().menuInflater.inflate(R.menu.menu_intruders_selection,menu)
                    return true
                }
                override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    return false
                }
                override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                    when(item?.itemId) {
                        R.id.action_private_message_delete -> {
                            viewModel.deleteChatRequest()
                        }
                    }
                    return false
                }
                override fun onDestroyActionMode(mode: ActionMode?) {
                    viewModel.exitSelectionMode()
                }
            }
            actionMode = (requireActivity() as MainActivity).startSupportActionMode(callback)
        }
        else {
            actionMode?.finish()
            actionMode = null
        }
    }

    private fun initAdapter() {
        Log.d("SUG", "initAdapter: create new adapter")
        mAdapter = PrivateMessagesAdapter(layoutInflater, viewModel.user.id, object: PrivateMessagesAdapter.ItemListener {
            override fun onItemClick(item: PrivateChat, position: Int) {
                if (viewModel.selectChat(item, position)) return
                navigateToChat(item.id,item.receiver)
            }

            override fun onItemLongClick(item: PrivateChat, position: Int) {
                viewModel.enterSelectionMode(item, position)
            }

            override fun isSelf(item: PrivateChat): Boolean {
                return item.receiverDetails.id == viewModel.user.id
            }

            override fun onLivePodiumIndicatorClicked(item: PrivateChat) {
                navigateToLivePodium(item)
            }
        })

        val layoutMan = LinearLayoutManager(context)

        binding.huddleList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            currentUserId = viewModel.user.id

            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)
                binding.newMessageFab.apply { if (binding.multiStateView.viewState==MultiStateView.ViewState.CONTENT
                    && tab == PrivateMessageTabType.BUDDIES) show() else hide() }
                viewModel.dataLoadingMore.postValue(loadState.append is LoadState.Loading)
            }
            registerAdapterDataObserver(object: RecyclerView.AdapterDataObserver() {
                override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                    super.onItemRangeMoved(fromPosition, toPosition, itemCount)
                    if ((fromPosition == 0 || toPosition == 0) && layoutMan.findFirstCompletelyVisibleItemPosition()==0) {
                        layoutMan.scrollToPosition(0)
                    }
                }
            })
        }
    }

    protected abstract fun navigateToMessageSearch()
    protected abstract fun navigateToPendingList()
    protected abstract fun navigateToBlockedMessages()
    protected abstract fun navigateToChat(roomId: String, receiver: Int)
    protected abstract fun navigateToPrivacy()
    //navigate to livepoidum
    protected  abstract fun navigateToLivePodium(item: PrivateChat)
}