package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.app.messej.data.model.AbstractUserWithStats
import com.app.messej.data.model.enums.FollowerType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.app.messej.data.room.EntityDescriptions
import com.google.gson.annotations.SerializedName

@Entity(
    tableName = EntityDescriptions.TABLE_BROADCAST_LIST
)
data class UserRelative(
    @SerializedName("id"            ) @ColumnInfo(name = "id"                   ) @PrimaryKey(autoGenerate = false) override val id           : Int = 0,
    @SerializedName("name"          ) @ColumnInfo(name = "name"                 ) override var name         : String = "",
    @SerializedName("thumbnail"     ) @ColumnInfo(name = "thumbnail"            ) override val thumbnail    : String? = null,
    @SerializedName("username"      ) @ColumnInfo(name = "username"             ) override val username     : String = "",

    @SerializedName("membership"    ) @ColumnInfo(name = "membership"           ) override val membership   : UserType = UserType.FREE,
    @SerializedName("verified"      ) @ColumnInfo(name = "verified"             ) override val verified     : Boolean = false,

    @SerializedName("dears"         ) @ColumnInfo(name = "dears"                ) override val dears        : Int     = 0,
    @SerializedName("fans"          ) @ColumnInfo(name = "fans"                 ) override val fans         : Int     = 0,
    @SerializedName("likers"        ) @ColumnInfo(name = "likers"               ) override val likers       : Int     = 0,
    @SerializedName("stars"         ) @ColumnInfo(name = "stars"                ) override val stars        : Int     = 0,

    @SerializedName("broadcast_likers_privacy" ) var broadcastLikersPrivacy : Boolean? = null,
    @SerializedName("is_followed"              ) var isFollowed             : Boolean? = null
): AbstractUserWithStats() {

    @ColumnInfo(name = "relative_type")
    var relativeType: FollowerType? = null

    override val citizenship: UserCitizenship
        get() = UserCitizenship.default()
}