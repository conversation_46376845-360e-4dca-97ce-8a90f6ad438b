package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.AbstractUserWithStats
import com.app.messej.data.model.entity.PrivateChat
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.google.gson.annotations.SerializedName

data class PrivateChatUserInfo(
    @SerializedName("id"                           ) override val id                         : Int,
    @SerializedName("membership"                   ) override val membership                 : UserType,
    @SerializedName("name"                         ) override val name                       : String,
    @SerializedName("profile_photo"                )          val profilePhoto               : String?  = null,
    @SerializedName("subscription_expiration_date" )          val subscriptionExpirationDate : String?  = null,
    @SerializedName("about"                        )          val about                      : String?  = null,
    @SerializedName("thumbnail"                    ) override val thumbnail                  : String?  = null,
    @SerializedName("username"                     ) override val username                   : String,
    @SerializedName("fans"                         ) override val fans                       : Int = 0,
    @SerializedName("dears"                        ) override val dears                      : Int = 0,
    @SerializedName("likers"                       ) override val likers                     : Int = 0,
    @SerializedName("stars"                        ) override val stars                      : Int = 0,
    @SerializedName("verified"                     ) override val verified                   : Boolean = false,
    @SerializedName("total_broadcasts"             )          val totalBroadcasts            : Int?     = null,
    @SerializedName("total_likes"                  )          val totalLikes                : Int?     = null,
    @SerializedName("chat_blocked"                 )          val chatBlocked                : Boolean? = null,
    @SerializedName("chat_type"                    )          val chatType                   : PrivateChat.ChatType?  = null,
    @SerializedName("citizenship"                  ) override val citizenship                : UserCitizenship? = UserCitizenship.default(),
): AbstractUserWithStats() {
}
//{
//    override val citizenship: UserCitizenship
//        get() = UserCitizenship.default()
//}
