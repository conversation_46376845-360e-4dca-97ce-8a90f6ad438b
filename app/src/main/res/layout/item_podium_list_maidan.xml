<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="podium"
            type="com.app.messej.data.model.entity.Podium" />
        <variable name="userRole" type="String" />
        <import type="com.app.messej.data.model.enums.PodiumEntry"/>
        <import type="com.app.messej.data.model.enums.PodiumKind"/>
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:foreground="?attr/selectableItemBackground">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/user_bubble"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_marginVertical="@dimen/activity_margin"
            android:layout_marginHorizontal="@dimen/activity_margin"
            android:animateLayoutChanges="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.makeramen.roundedimageview.RoundedImageView
                android:id="@+id/user_dp"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                app:imageUrl="@{podium.thumbnail}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="W,1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:placeholder="@{@drawable/im_user_placeholder_square}"
                app:riv_oval="true"
                tools:src="@drawable/im_user_placeholder_square" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.3" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/premium_badge"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:elevation="4dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="@id/guideline"
                tools:src="@drawable/ic_user_badge_premium" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_data"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/activity_margin"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/user_action"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toEndOf="@+id/user_bubble"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/podium_name"
                style="@style/TextAppearance.Flashat.Headline6"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/textColorSecondary"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Angel" />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/live_status_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{podium.live? @string/title_podium_live: @string/title_podium_offline}"
                android:textAppearance="@style/TextAppearance.Flashat.Caption.Bold"
                android:textColor="@{podium.live? @color/colorPass: @color/colorError}"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/podium_name"
                android:layout_marginTop="@dimen/element_spacing"
                tools:text="@string/title_podium_live"
                tools:visibility="visible" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/live_counter"
                goneIfNot="@{podium.live}"
                android:layout_marginStart="@dimen/element_spacing"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="@+id/live_status_text"
                app:layout_constraintStart_toEndOf="@id/live_status_text"
                app:layout_constraintTop_toTopOf="@+id/live_status_text">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/live_img"
                    android:layout_width="10dp"
                    android:layout_height="wrap_content"
                    android:adjustViewBounds="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/bg_generic_circle"
                    app:tint="@color/colorPass" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/live_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/line_spacing"
                    android:text="@{podium.liveUsersFormatted}"
                    android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                    android:textColor="@color/textColorSecondary"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/live_img"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="25" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/user_action"
            style="@style/Widget.Flashat.MaidanListButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:icon="@{podium.canChallenge?@drawable/ic_podium_maidan_fist_filled:@drawable/ic_podium_maidan_watch}"
            tools:icon="@drawable/ic_podium_maidan_watch"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.divider.MaterialDivider
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:layout_editor_absoluteX="16dp" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>