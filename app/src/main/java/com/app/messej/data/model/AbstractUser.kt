package com.app.messej.data.model

import com.app.messej.data.model.enums.UserBadge
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType

abstract class AbstractUser {
    abstract val id           : Int
    abstract val name         : String
    abstract val thumbnail    : String?
    abstract val username     : String?

    abstract val membership   : UserType
    abstract val verified     : Boolean
    abstract val citizenship  : UserCitizenship?
    open val countryCode  : String?
        get() = null

    val premiumUser: Boolean
        get() {
            return membership == UserType.PREMIUM
        }

    val userBadge: UserBadge
        get() {
            return if(verified) UserBadge.VERIFIED
            else if(premiumUser) UserBadge.PREMIUM
            else UserBadge.NONE
        }

    fun asBasicUser() = BasicUser(
        id = id,
        name = name,
        thumbnail = thumbnail,
        username = username,
        membership = membership,
        verified = verified,
        citizenship = citizenship,
        countryCode = countryCode
    )

    data class BasicUser(
        override val id: Int,
        override val name: String,
        override val thumbnail: String?,
        override val username: String?,
        override val membership: UserType,
        override val verified: Boolean,
        override val citizenship: UserCitizenship?,
        override val countryCode: String?,
    ): AbstractUser()
}
