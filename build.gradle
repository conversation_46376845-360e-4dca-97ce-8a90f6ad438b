buildscript {
    ext {
        kotlin_version = '2.1.0'
        agp_version = '8.13.0-alpha02'
    }
    dependencies {
        classpath 'com.google.gms:google-services:4.4.2'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.2'
        classpath "androidx.navigation:navigation-safe-args-gradle-plugin:2.8.5"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
//        classpath "com.google.android.libraries.mapsplatform.secrets-gradle-plugin:secrets-gradle-plugin:2.0.1"

    }
    repositories {
        google()
        mavenCentral()
    }
}// Top-level build file where you can add configuration options common to all sub-projects/modules.

plugins {
    id 'com.android.application' version '8.13.0-alpha02' apply false
    id 'com.android.library' version '8.11.1' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.24' apply false
    id 'org.jetbrains.kotlin.kapt' version '1.9.22' apply false
    id 'org.jetbrains.kotlin.plugin.compose' version '2.1.0' apply false
    id 'com.google.devtools.ksp' version '2.1.0-1.0.29' apply false
}