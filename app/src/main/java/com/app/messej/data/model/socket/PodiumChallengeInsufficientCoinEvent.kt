package com.app.messej.data.model.socket

import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.enums.ChallengeContributionType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.google.gson.annotations.SerializedName

data class PodiumChallengeInsufficientCoinEvent(
    @SerializedName("id"                ) override val id              : Int,
    @SerializedName("user_id"           ) val userId          : Int?     = null,
    @SerializedName("coins_spent"       ) val coinsSpent      : Double?     = null,
    @SerializedName("time_request_sent" ) val timeRequestSent : String?  = null,
    @SerializedName("self_requested"    ) val selfRequested   : Boolean? = null,
    @SerializedName("request_accepted"  ) val requestAccepted : Boolean? = null,
    @SerializedName("appointed_by"      ) val appointedBy     : Int?     = null,
    @SerializedName("time_responded"    ) val timeResponded   : String?  = null,
    @SerializedName("name"              ) override val name            : String,
    @SerializedName("username"          ) override val username        : String,
    @SerializedName("membership"        ) override val membership      : UserType,
    @SerializedName("profile_url"       ) val profileUrl      : String?  = null,
    @SerializedName("thumbnail"         ) override val thumbnail       : String?  = null,
    @SerializedName("verified"          ) override val verified        : Boolean,
    @SerializedName("citizenship"       ) override val citizenship     : UserCitizenship? = null,
    @SerializedName("country_code"      ) override val countryCode     : String?  = null,
    @SerializedName("enable_camera"     ) val enableCamera    : Boolean? = null,
    @SerializedName("podium_id"         ) val podiumId        : String,
    @SerializedName("challenge_id"      ) val challengeId     : String,
    @SerializedName("contributor_type"  ) val contributorType : ChallengeContributionType
) : AbstractUser() {
}
