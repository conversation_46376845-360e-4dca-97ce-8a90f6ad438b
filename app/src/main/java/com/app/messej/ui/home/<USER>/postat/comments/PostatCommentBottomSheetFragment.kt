package com.app.messej.ui.home.publictab.postat.comments

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.NavProfileDirections
import com.app.messej.R
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.PostatCommentLikePayload
import com.app.messej.data.model.api.postat.PostComment
import com.app.messej.data.model.entity.PostReply
import com.app.messej.databinding.FragmentPostatCommentBottomSheetBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureInteractionAllowed
import com.app.messej.ui.enforcements.EnforcementExtensions.ensurePostatPostingAllowed
import com.app.messej.ui.home.publictab.flash.comments.CommentsListener
import com.app.messej.ui.utils.ExpandableListBottomSheetDialogFragment
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.confirmPaidLikeAction
import com.app.messej.ui.utils.FragmentExtensions.showInsufficientBalanceAlert
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.TextFormatUtils
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences
import com.bumptech.glide.Glide
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog

class PostatCommentBottomSheetFragment : ExpandableListBottomSheetDialogFragment() {

    private lateinit var binding: FragmentPostatCommentBottomSheetBinding
    private val args: PostatCommentBottomSheetFragmentArgs by navArgs()
    private val viewModel: PostatCommentsViewModel by viewModels()


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_FlashCommentsBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_postat_comment_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = true
        setup()
        initAdapter()
        observer()
    }

    private fun initAdapter() {
        binding.messagesList.setContent {
            PostatCommentsRepliesScreen(viewModel = viewModel, listener = object : CommentsListener {
                override fun onReplyClicked(postatComment: PostComment?, reply: PostReply?) {
                    onReplayAction(postatComment,reply)
                }

                override fun onFlashLikeClicked(commentId: String?, replayId: String?) {}  /*for delete flash only*/

                override fun onPostatLikeClicked(payload: PostatCommentLikePayload) {
                    if(viewModel.isLikeLoading.value==true)return
                    ensureInteractionAllowed {
                        if (viewModel.skipNextTime.value == false) {
                            confirmPaidLikeAction(
                                message = R.string.podium_paid_like_confirmation,
                                skipNextTime = viewModel.skipNextTime.value == true
                            ) { skipSelected ->
                                viewModel.setUpPaidLike(skipSelected)
                                viewModel.sendCommentPaidLike(payload)
                            }
                        } else {
                            viewModel.sendCommentPaidLike(payload)

                        }
                    }
                }


                override fun onReportClicked(data: PostComment) {}

                override fun onDeleteFlashClicked(commentId: String, reply: Boolean?, userId: Int) {}

                override fun onDeletePostatClicked(messageId: String, commentId: String, reply: Boolean?, userId: Int) {
                    if(userId != viewModel.user.id){
                        showToast("You Cant Delete Others message")
                        return
                    }
                    reply?.let { viewModel.validateDeleteAction(it, messageId, commentId) }
                }


                override fun onProfileImageClicked(userId: Int) {
                    findNavController().navigateSafe(PostatCommentBottomSheetFragmentDirections.actionGlobalPublicUserProfileFragment(userId))
                }

            })
        }
    }

    private fun observer() {

        viewModel.onNavigateToPrivateMessage.observe(viewLifecycleOwner) {
            val action = NavProfileDirections.actionGlobalNavigationChatPrivate(it.first, it.second)
            (activity as MainActivity).navController.navigateSafe(action)
        }

        viewModel.onCommentAdded.observe(viewLifecycleOwner) {
            binding.layoutReply.visibility = View.GONE
        }

        viewModel.skipNextTime.observe(viewLifecycleOwner) {
            Log.w("SKipNxtTime", "observe: skipPaidLikeConfirmation: ${it}")
        }
        viewModel.isLikeLoading.observe(viewLifecycleOwner) {
            Log.d("LIKE_ENABLED", "observe: ${it}")
        }
        viewModel.onPaidLikeInsufficientBalance.observe(viewLifecycleOwner) {
            if (it) {
                showInsufficientBalanceAlert(R.string.podium_paid_like_balance_error)
            }
        }
        viewModel.onCommentDeleted.observe(viewLifecycleOwner){
            binding.undoCommentContainer.visibility = if(it) View.VISIBLE else View.GONE
        }

    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setup() {
        viewModel.setPostId(args.postId)
        viewModel.getSkipAction()
        binding.commentSendButton.setOnClickListener {
            ensurePostatPostingAllowed {
                viewModel.writeComment()
                viewModel.setSelectedCommentId(null)
                viewModel.setSelectedReplyCommentId(null)
            }
        }
        snapToBottom(binding.commentMessageContainer,binding.root)

        binding.commentInput.editText?.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                (dialog as? BottomSheetDialog)?.behavior?.apply {
                    state = BottomSheetBehavior.STATE_EXPANDED
                }
            }
        }
//        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
//        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)


        binding.btnClose.setOnClickListener {
            binding.layoutReply.visibility = View.GONE
            binding.inputComment.setText("")
            viewModel.postMentionedUser(null)
        }
        binding.textUndo.setOnClickListener {
            viewModel.undoComment()
        }

        binding.inputComment.setOnTouchListener { view, event ->
            view.parent.requestDisallowInterceptTouchEvent(true)
            false
        }
    }

    private fun confirmCommentDelete(messageId: String, commentId: String) {

        confirmAction(message = getString(R.string.postat_delete_comment)){
            viewModel.deletePostatComment(messageId, commentId)
        }
    }

    override fun onResume() {
        super.onResume()
//        mAdapter.refresh()
    }



    fun onReplayAction(postatComment: PostComment?, reply: PostReply?) {
        val comment = reply ?: postatComment
        val sender = comment?.senderDetails
        val commentId = if (reply != null) reply.parentCommentId else comment?.commentId
        Log.d("REPLAY", "onReplayAction: $reply")
        viewModel.postMentionedUser(sender)
        commentId?.let { viewModel.setCommentId(it) }

        binding.apply {
            layoutReply.visibility = View.VISIBLE
            badgePremium.isVisible = sender?.premium == true
            if(reply!=null){
                val result = comment?.comment?.replace(Regex("#[^#]+#"), "")?.trim()?:""
                replyingText.text =resources.getString(R.string.replying_to, sender?.name ?: "", result)
            }else {
                replyingText.text =    resources.getString(R.string.replying_to, sender?.name, comment?.comment)}
            Glide.with(requireContext()).load(sender?.thumbnail).placeholder(R.drawable.im_user_placeholder_opaque).into(imagePersonPhoto)
        }

        binding.inputComment.apply {
            val str = resources.getString(R.string.chat_replay_item, sender?.name)
            val mentionedUser = str.highlightOccurrences(str) {
                ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.colorPrimaryDark))
            }
            setText(mentionedUser)
            setSelection(mentionedUser.length)
            Log.d("MentionedUser", "${mentionedUser}")
            viewModel.setMentionedUser(mentionedUser.toString())
        }

    }
}
