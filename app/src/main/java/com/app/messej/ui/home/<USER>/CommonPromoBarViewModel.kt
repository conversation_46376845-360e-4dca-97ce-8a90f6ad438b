package com.app.messej.ui.home.promobar

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.socket.PromoAnnouncement
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.AppRepository
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.socket.repository.PromoBoardEventRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.home.promobar.CommonPromoBarUtil.clearAllPromos
import com.app.messej.ui.home.promobar.CommonPromoBarUtil.clearPromo
import com.app.messej.ui.home.promobar.CommonPromoBarUtil.insertPromo
import com.app.messej.ui.utils.AsyncExtensions.collectAsync
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.Duration
import java.time.LocalDateTime


class CommonPromoBarViewModel(application: Application) : AndroidViewModel(application) {
    private val accountRepo: AccountRepository = AccountRepository(application)
    private val promoEventRepo = PromoBoardEventRepository

    private val appRepo: AppRepository = AppRepository(application)

    val user: CurrentUser get() = accountRepo.user

    val citizenshipFlow = accountRepo.citizenShipFlow.distinctUntilChanged()

    init {
        collectAsync(promoEventRepo.announcementFLow) {
            Log.d("ANNC", "maidanAnnouncementFLow: $it")
            /*
            If the item is AdminAnnouncementScheduled it directly added to the current promo cycle and that will show immediately in promo bar.
            if that AdminAnnouncementScheduled with repeating == true, add it to the promos.
            else not adding to the promos. Just show one time in the promo bar.
            */
            if (it is PromoAnnouncement.AdminAnnouncementScheduled) {
                currentPromoCycle.add(0, it)
                if (it.repeat != true) return@collectAsync
            }
            promos.insertPromo(it)
        }

        collectAsync(promoEventRepo.announcementClearFLow) {
            Log.d("ANNC", "maidanAnnouncementFLow: $it")
            promos.clearPromo(it)
        }

        collectAsync(citizenshipFlow) {
            Log.d("ANNC", "Citizenship flow: Citizenship-> $it")
            //Clearing all promos when citizenship changes.
            promos.clearAllPromos()
            //Calling announcement api on viewmodel init and also when citizenship changes
            fetchPromoHistory()
        }
        cycleAnnouncements()
    }

    companion object {
        const val DEFAULT_DELAY = 3000L
    }

    private var promos: PromoBarQueue = mutableListOf()
    private var currentPromoCycle = mutableListOf<PromoAnnouncement>()

    private val _promoCycle = MutableLiveData<PromoAnnouncement?>(null)
    val promoCycle: LiveData<PromoAnnouncement?> = _promoCycle.distinctUntilChanged()

    private var calculatedDelay: Long = DEFAULT_DELAY

    fun setDelay(delay: Long) {
        Log.d("ANNC", "setting delay $delay")
        calculatedDelay = delay
    }

    private var promoBarResetAt: LocalDateTime? = null

    fun extendDelayForReset() {
        promoBarResetAt = LocalDateTime.now()
        Log.d("ANNC", "delay reset")
    }

    private fun cycleAnnouncements() {
        viewModelScope.launch(Dispatchers.Default) {
            fun next(): PromoAnnouncement? {
                if (currentPromoCycle.isEmpty()) {
                    if (promos.isEmpty()) {
                        _promoCycle.postValue(null)
                        return null
                    } else {
                        currentPromoCycle.addAll(promos)
                        promos = promos.filterNot { it is PromoAnnouncement.AdminAnnouncementScheduled && it.repeat != true }.toMutableList()
                    }
                }
                Log.d("ANNC", "promo cycle | current: ${currentPromoCycle.size} items | base: ${promos.size} items")
                val next = currentPromoCycle.removeAt(0)
                _promoCycle.postValue(next)
                Log.d("ANNC", "post next announcement: $next")
                return next
            }
            delay(5000)
            while (true) {
                if (isActive) {
                    Log.d("ANNC", "delay before next ${calculatedDelay - DEFAULT_DELAY}")
                    // wait for any additional time that is needed over the default delay
                    delay((calculatedDelay - DEFAULT_DELAY).coerceAtLeast(0))

                    promoBarResetAt?.let { reset ->
                        val elapsedMs = Duration.between(reset, LocalDateTime.now()).toMillis().coerceAtLeast(0)
                        // add additional delay to account for promo bar resetting
                        Log.d("ANNC", "additional delay due to reset ${calculatedDelay - elapsedMs}")
                        delay((calculatedDelay - elapsedMs).coerceAtLeast(0))
                        promoBarResetAt = null
                    }

                    calculatedDelay = DEFAULT_DELAY
                    next()
                    Log.d("ANNC", "delay after next $DEFAULT_DELAY")
                    delay(DEFAULT_DELAY)
                }
            }
        }
    }

    private fun fetchPromoHistory() {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = appRepo.getPromoHistory()) {
                is ResultOf.APIError -> {
                    Log.e("ANNC", "failed to fetch promos", Exception(result.errorMessage()))
                }
                is ResultOf.Error -> {
                    Log.e("ANNC", "failed to fetch promos", result.exception)
                }
                is ResultOf.Success -> {
                    withContext(Dispatchers.Main) {
                        val list = result.value.flatList
                        list.forEach {
                            Log.d("ANNC", "fetchPromoHistory: $it")
                            promos.insertPromo(it)
                        }
                    }
                }
            }
        }
    }

    private val podiumRepository = PodiumRepository(application)

    fun checkIfPodiumLive(podiumId: String, onLive: (Boolean) -> Unit) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                Log.d("PodiumLVM", "trying to leave")
                when (val result = podiumRepository.getPodiumDetails(podiumId)) {
                    is ResultOf.Success -> {
                        Log.d("PodiumLVM", "leave success")
                        withContext(Dispatchers.Main) {
                            onLive.invoke(result.value.isLive)
                        }
                    }

                    is ResultOf.APIError -> {}

                    is ResultOf.Error -> {}
                }

            } catch (e: Exception) {
                Log.d("PodiumLVM", "leave: error: ${e.message}")
            }
        }
    }

}