package com.app.messej.data.model.socket

import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.UserRatingProvider
import com.app.messej.data.model.api.podium.UserStats
import com.app.messej.data.model.enums.PodiumLiveChatType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.google.gson.annotations.SerializedName

data class PodiumLiveChat(
//    @SerializedName("eventName"     ) var eventName    : String = SocketEvent.RX_TX_PODIUM_CHAT.key,
    @SerializedName("podium_id"     ) val podiumId     : String,
    @SerializedName("chat_id"       ) val chatId       : String,
    @SerializedName("user_stats"    ) var userStats: UserStats? = null,
    @SerializedName("message"       ) val message      : String,
    @SerializedName("created"       ) val created      : String,
    @SerializedName("sender_detail" ) val senderDetails: SenderDetails,
    @SerializedName("user_id"       ) val userId       : Int,
    @SerializedName("country_code"  ) val countryCode  : String?,
    @SerializedName("chat_type"     ) private val _chatType     : PodiumLiveChatType? = PodiumLiveChatType.NORMAL,
    @SerializedName("user_tribe_name"   ) val userTribeName   : String? = null,

    ): SocketEventPayload() {

        var chatFrozenForUser: Boolean = false

        val chatType: PodiumLiveChatType
            get() = _chatType ?: PodiumLiveChatType.NORMAL

        abstract inner class UserWithRating: AbstractUser(), UserRatingProvider

        val userWithRating: UserWithRating
            get() = object : UserWithRating() {
                override val id: Int
                    get() = userId
                override val name: String
                    get() = senderDetails.name
                override val thumbnail: String?
                    get() = senderDetails.thumbnail
                override val username: String
                    get() = senderDetails.username
                override val membership: UserType
                    get() = senderDetails.membership
                override val verified: Boolean
                    get() = senderDetails.verified
                override val userRating: Double
                    get() = (userStats?.rating ?: 0).toDouble() / 100
                override val citizenship: UserCitizenship?
                    get() = senderDetails.citizenship

                override val countryCode: String?
                    get() = <EMAIL>

            }
    }
