<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable name="viewModel" type="com.app.messej.ui.home.publictab.flash.create.FlashRecordViewModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:applySystemBarInsets="@{`ime|bottom`}">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fitsSystemWindows="true"
                app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/close_button"
                    style="@style/Widget.Flashat.Button.TextButton.IconOnly"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/activity_margin"
                    app:icon="@drawable/ic_back_button"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/flash_create_title"
                    android:textAppearance="@style/TextAppearance.Flashat.Headline5"
                    android:textColor="@color/textColorSecondary"
                    app:layout_constraintBottom_toBottomOf="@+id/close_button"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/close_button"
                    app:layout_constraintTop_toTopOf="@+id/close_button" />

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/materialCardView2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/activity_margin"
                    android:elevation="0dp"
                    app:cardCornerRadius="8dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/close_button">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <androidx.media3.ui.PlayerView
                            android:id="@+id/player_view"
                            style="@style/Widget.Flashat.VideoPlayer"
                            android:layout_width="150dp"
                            android:layout_height="0dp"
                            app:controller_layout_id="@layout/layout_flash_player_controller"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintDimensionRatio="h,9:16"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <include
                            android:id="@+id/encode_progress"
                            layout="@layout/layout_video_encode_progress_card_small"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:elevation="4dp"
                            app:goneIf="@{viewModel.flashVideoProgress==null}"

                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/text_input_caption"
                    style="@style/Widget.Flashat.OutlinedTextInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/extra_margin"
                    android:layout_marginTop="@dimen/extra_margin"
                    app:counterEnabled="true"
                    app:counterMaxLength="512"
                    app:hintAnimationEnabled="false"
                    app:hintEnabled="false"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/materialCardView2">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/text_input_huddle_bio"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="top"
                        android:hint="@string/flash_create_caption_hint"
                        android:inputType="textMultiLine"
                        android:minLines="1"
                        android:maxLines="8"
                        android:maxLength="512"
                        android:text="@={viewModel.flashCaption}" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/materialCardViewComments"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/extra_margin"
                    android:layout_marginTop="@dimen/activity_margin"
                    app:cardBackgroundColor="@color/colorSurfaceSecondaryDark"
                    app:cardCornerRadius="12dp"
                    app:layout_constraintTop_toBottomOf="@+id/text_input_caption"
                    tools:layout_editor_absoluteX="24dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/flash_text_pause_comments"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            style="@style/TextAppearance.Flashat.Body1"
                            android:text="@string/flash_text_pause_comments"
                            android:padding="@dimen/activity_margin"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"/>

                        <com.google.android.material.materialswitch.MaterialSwitch
                            android:id="@+id/switch_pause_comments"
                            style="@style/Widget.Flashat.Switch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="@{!viewModel.isCommentsDisabled}"
                            android:layout_marginEnd="@dimen/activity_margin"
                            app:layout_constraintBottom_toBottomOf="@id/flash_text_pause_comments"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="@id/flash_text_pause_comments" />


                    </androidx.constraintlayout.widget.ConstraintLayout>

                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/category_dropdown"
                    style="@style/Widget.Flashat.OutlinedTextInput.ExposedDropdownMenu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/extra_margin"
                    android:layout_marginTop="@dimen/activity_margin"
                    app:hintAnimationEnabled="false"
                    app:hintEnabled="false"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/materialCardViewComments"
                    tools:text="Male">

                    <AutoCompleteTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:dropDownAnchor="@id/category_dropdown"
                        android:dropDownWidth="wrap_content"
                        android:dropDownHeight="wrap_content"
                        android:hint="@string/flash_create_category_hint"
                        android:inputType="none" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/materialCardView3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/extra_margin"
                    android:layout_marginTop="@dimen/activity_margin"
                    app:cardBackgroundColor="@color/colorSurfaceSecondaryDark"
                    app:cardCornerRadius="12dp"
                    app:layout_constraintTop_toBottomOf="@+id/category_dropdown"
                    tools:layout_editor_absoluteX="24dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <androidx.constraintlayout.widget.Guideline
                            android:id="@+id/guideline2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintGuide_percent="0.45"
                            app:layout_constraintStart_toStartOf="parent" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/activity_margin"
                            android:layout_marginEnd="@dimen/element_spacing"
                            android:text="@string/flash_create_share_to"
                            android:textAlignment="textEnd"
                            android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                            android:textColor="@color/textColorSecondary"
                            app:layout_constraintBottom_toBottomOf="@+id/share_to_public"
                            app:layout_constraintEnd_toStartOf="@+id/guideline2"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/share_to_public" />

                        <CheckBox
                            android:id="@+id/share_to_public"
                            style="@style/Widget.Flashat.Checkbox"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/element_spacing"
                            android:layout_marginTop="@dimen/element_spacing"
                            android:layout_marginEnd="@dimen/activity_margin"
                            android:minHeight="36dp"
                            android:enabled="@{viewModel.disableSharePublic == false}"
                            android:checked="@={viewModel.flashSharePublic}"
                            android:text="@string/flash_create_share_to_public"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="@+id/guideline2"
                            app:layout_constraintTop_toTopOf="parent" />

                        <CheckBox
                            android:id="@+id/share_to_dears"
                            style="@style/Widget.Flashat.Checkbox"
                            android:checked="@={viewModel.flashShareDears}"
                            android:enabled="@{viewModel.flashSharePublic == false}"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/element_spacing"
                            android:layout_marginEnd="@dimen/activity_margin"
                            android:minHeight="36dp"
                            android:text="@string/profile_dears"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="@+id/guideline2"
                            app:layout_constraintTop_toBottomOf="@+id/share_to_public" />

                        <CheckBox
                            android:id="@+id/share_to_fans"
                            style="@style/Widget.Flashat.Checkbox"
                            android:checked="@={viewModel.flashShareFans}"
                            android:enabled="@{viewModel.flashSharePublic == false}"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/element_spacing"
                            android:layout_marginEnd="@dimen/activity_margin"
                            android:minHeight="36dp"
                            android:text="@string/profile_fans"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="@+id/guideline2"
                            app:layout_constraintTop_toBottomOf="@+id/share_to_dears" />

                        <CheckBox
                            android:id="@+id/share_to_likers"
                            style="@style/Widget.Flashat.Checkbox"
                            android:checked="@={viewModel.flashShareLikers}"
                            android:enabled="@{viewModel.flashSharePublic == false}"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/element_spacing"
                            android:layout_marginEnd="@dimen/activity_margin"
                            android:layout_marginBottom="@dimen/element_spacing"
                            android:minHeight="36dp"
                            android:text="@string/profile_likers"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="@+id/guideline2"
                            app:layout_constraintTop_toBottomOf="@+id/share_to_fans" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/post_button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    style="@style/Widget.Flashat.LargeRoundedButton"
                    android:textAllCaps="false"
                    android:layout_marginEnd="@dimen/extra_margin"
                    android:layout_marginTop="@dimen/extra_margin"
                    android:text="@string/flash_create_post"
                    android:enabled="@{viewModel.enablePostButton}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/draft_button"
                    app:layout_constraintTop_toBottomOf="@+id/materialCardView3" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/draft_button"
                    style="@style/Widget.Flashat.LargeRoundedButton.Inverse"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/extra_margin"
                    android:layout_marginEnd="@dimen/extra_margin"
                    android:text="@string/flash_create_draft"
                    android:textAllCaps="false"
                    android:enabled="@{viewModel.processedVideo!=null}"
                    app:layout_constraintBottom_toBottomOf="@+id/post_button"
                    app:layout_constraintEnd_toStartOf="@+id/post_button"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/post_button"
                    app:layout_constraintVertical_bias="0.0" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>
