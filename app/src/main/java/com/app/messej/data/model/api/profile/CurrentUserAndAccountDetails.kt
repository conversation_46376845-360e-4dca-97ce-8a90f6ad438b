package com.app.messej.data.model.api.profile;

import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType

abstract class CurrentUserAndAccountDetails: AbstractUser() {
    abstract override val id               : Int
    abstract override val username         : String
    abstract override val verified         : Boolean
    abstract val email                     : String?
    abstract val phone                     : String?
    abstract override val countryCode               : String?
    abstract val countryCodeIso            : String?
    abstract val profileCompletePercentage : Int
    abstract val blockedByAdmin                        : Boolean?
    abstract override val membership       : UserType
    abstract override val citizenship: UserCitizenship
    abstract val citizenshipPriority : Int?
    abstract val userEmpowerment           : CurrentUser.UserEmpowerment?
    abstract val isFlashBlocked : Boolean?
    abstract val fans                      : Int
    abstract val dears                     : Int
    abstract val stars                     : Int
    abstract val likers                    : Int
    abstract val userLevelAnimationUrl : String?
    abstract val locationChanged : Boolean?
    abstract val activePoints: Double?
    abstract var totalGiftPoints: Double?
    abstract val isFlixSubscriptionRenewed : Boolean?
    abstract val userFunctionalityBlocks : CurrentUser.UserFunctionalityBlocks?
    abstract val coinsNeededNextContributorLevel : Double?
    abstract val gamesNeededNextPlayerLevel : Double?
    abstract val enforcementStatus: UserEnforcements.EnforcementStatus?
    abstract val superStarId: Int?

    companion object {

//        fun copyCommonFields(from: CurrentUserAndAccountDetails, to: CurrentUserAndAccountDetails) {
//            CurrentUserAndAccountDetails::class.members.forEach { property ->
//                if (property is KMutableProperty1) {
//                    val value = property.get(from)
//                    property.set(to, value)
//                }
//            }
//        }

        fun CurrentUser.copyCommonFieldsFrom(account: AccountDetailsResponse): CurrentUser {
            val updatedUser = this.copy(
                profile = profile.copy(
                    name = account.name,
                    dateOfBirth = account.dob,
                    genderString = account.gender,
                    about = account.about,
                    profilePhoto = account.profilePhoto,
                    thumbnail = account.thumbnail,
                    referralLink = account.referralLink,
                    totalPerformancePoints = account.totalPerformancePoints,
                    verified = account.verified,
                    userRatingPercentage = account.flaxRatePercentage?.toDouble(),
                    premium = account.isPremium == true
                ),
                id = account.id,
                totalGiftPoints = account.totalGiftPoints,
                username = account.username,
                verified = account.verified,
                email = account.email,
                phone = account.phone,
                countryCode = account.countryCode,
                countryCodeIso = account.countryCodeIso,
                profileCompletePercentage = account.profileCompletePercentage,
                blockedByAdmin = account.blockedByAdmin,
                membership = account.membership,
                citizenship = account.citizenship,
                citizenshipPriority = account.citizenshipPriority,
                userEmpowerment = account.userEmpowerment,
                isFlashBlocked = account.isFlashBlocked,
                fans = account.fans,
                dears = account.dears,
                stars = account.stars,
                likers = account.likers,
                userLevelAnimationUrl = account.userLevelAnimationUrl,
                locationChanged = account.locationChanged,
                activePoints = account.activePoints,
                isFlixSubscriptionRenewed = account.isFlixSubscriptionRenewed,
                userFunctionalityBlocks = account.userFunctionalityBlocks,
                coinsNeededNextContributorLevel = account.coinsNeededNextContributorLevel,
                gamesNeededNextPlayerLevel = account.gamesNeededNextPlayerLevel,
                enforcementStatus = account.enforcementStatus,
                superStarId = account.superStarId,
                tribeName = account.userTribeName
            )
            val sed = account.subscriptionExpirationDate
            updatedUser.profile.subscriptionExpiration = sed?.toLocalDateTime()
            return updatedUser
        }
    }
}
