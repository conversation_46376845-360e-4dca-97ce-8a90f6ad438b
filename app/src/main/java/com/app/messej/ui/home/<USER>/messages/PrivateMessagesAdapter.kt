package com.app.messej.ui.home.privatetab.messages

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.AbstractChatMessage.MessageType
import com.app.messej.data.model.entity.PrivateChat
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.utils.MediaUtils
import com.app.messej.databinding.ItemChatListBinding
import com.app.messej.ui.utils.DateFormatHelper
import com.app.messej.ui.utils.TextFormatUtils.applyMarkdownFormatting

class PrivateMessagesAdapter(private val inflater: LayoutInflater, private val userId: Int, private val mListener: ItemListener): PagingDataAdapter<PrivateMessagesAdapter.PrivateChatUIModel, PrivateMessagesAdapter.PrivateMessageViewHolder>(
    ChatDiff
) {

    sealed class PrivateChatUIModel {
        abstract val privateChat: PrivateChat
        class ChatUIModel(override val privateChat: PrivateChat, var selected: Boolean = false) : PrivateChatUIModel()
    }
    interface ItemListener {
        fun onItemClick(item: PrivateChat, position: Int)
        fun onItemLongClick(item: PrivateChat, position: Int) {}

        fun isSelf(item: PrivateChat): Boolean

        fun onLivePodiumIndicatorClicked(item: PrivateChat)
    }

    var currentUserId: Int? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        PrivateMessageViewHolder(
            ItemChatListBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )

    override fun onBindViewHolder(holder: PrivateMessageViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it,position) }
    }

    inner class PrivateMessageViewHolder(private val binding: ItemChatListBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PrivateChatUIModel, position: Int) = with(binding) {
            item as PrivateChatUIModel.ChatUIModel
            chat = item.privateChat
            selected = item.selected
            isSelf = mListener.isSelf(item.privateChat)
            currentUser = currentUserId
            huddleDp.setOnClickListener {
                if (item.privateChat.receiverDetails.userLivePodium && item.privateChat.receiverDetails.userLivePodiumId != null && !mListener.isSelf(item.privateChat)) {
                    mListener.onLivePodiumIndicatorClicked(item.privateChat)
                }
            }
            root.setOnClickListener { mListener.onItemClick(item.privateChat,position) }
            root.setOnLongClickListener { mListener.onItemLongClick(item.privateChat, position); true}
            huddleTime.text = DateFormatHelper.humanizeMessageTime(item.privateChat.parsedUpdated, root.context)
            showLastMessagePreview(this, item.privateChat)
        }
    }

    private fun showLastMessagePreview(binding: ItemChatListBinding,item: PrivateChat) = with(binding) {
        huddleLastMessageMediaIcon.visibility = View.GONE
        huddleLastMessage.text = ""
        item.lastMessage?.let { lm ->
            if(lm.deleted) {
                huddleLastMessage.setText(R.string.chat_message_deleted)
            }
            else {
                val formattedMessage = lm.displayMessage.orEmpty().applyMarkdownFormatting(root.context)
                when (lm.messageType) {
                    MessageType.ACTIVITY, MessageType.TEXT, MessageType.GIFT -> {
                        huddleLastMessage.text = formattedMessage
                    }
                    MessageType.POLL -> {
                        huddleLastMessage.text = lm.displayMessage
                    }
                    MessageType.LOCATION -> {
                        huddleLastMessageMediaIcon.apply {
                            visibility = View.VISIBLE
                            setImageDrawable(ContextCompat.getDrawable(binding.root.context, R.drawable.ic_chat_location_14dp))
                        }
                        huddleLastMessage.setText(R.string.message_list_preview_location)
                    }

                    MessageType.STICKER -> {
                        huddleLastMessageMediaIcon.apply {
                            visibility = View.VISIBLE
                            setImageDrawable(ContextCompat.getDrawable(binding.root.context, R.drawable.ic_chat_sticker_14dp))
                        }
                        huddleLastMessage.setText(R.string.chat_sticker)
                    }

                    MessageType.MEDIA -> {
                        when (lm.mediaMeta?.mediaType) {
                            MediaType.IMAGE -> {
                                huddleLastMessageMediaIcon.apply {
                                    visibility = View.VISIBLE
                                    setImageDrawable(ContextCompat.getDrawable(binding.root.context, R.drawable.ic_chat_image_14dp))
                                }

                                if (!lm.displayMessage.isNullOrBlank()) huddleLastMessage.text = formattedMessage else huddleLastMessage.setText(R.string.message_list_preview_photo)
                            }

                            MediaType.AUDIO -> {
                                huddleLastMessageMediaIcon.apply {
                                    visibility = View.VISIBLE
                                    setImageDrawable(ContextCompat.getDrawable(binding.root.context, R.drawable.ic_chat_audio_14dp))
                                }

                                if (!lm.displayMessage.isNullOrBlank()) huddleLastMessage.text = formattedMessage else huddleLastMessage.text = lm.mediaMeta?.mediaDuration

                            }

                            MediaType.VIDEO -> {
                                huddleLastMessageMediaIcon.apply {
                                    visibility = View.VISIBLE
                                    setImageDrawable(ContextCompat.getDrawable(binding.root.context, R.drawable.ic_chat_video_14dp))
                                }

                                if (!lm.displayMessage.isNullOrBlank()) huddleLastMessage.text = formattedMessage else huddleLastMessage.text = lm.mediaMeta?.mediaDuration
                            }

                            MediaType.DOCUMENT -> {
                                huddleLastMessageMediaIcon.apply {
                                    visibility = View.VISIBLE
                                    lm.mediaMeta?.let {
                                        val res = MediaUtils.getDocumentResFromType(it.mediaDocumentType)
                                        setImageDrawable(ContextCompat.getDrawable(binding.root.context, res))
                                    }
                                }

                                huddleLastMessage.text = formattedMessage
                            }

                            else -> {}
                        }
                    }
                }
            }
        }
    }


    object ChatDiff : DiffUtil.ItemCallback<PrivateChatUIModel>() {
        override fun areItemsTheSame(oldItem: PrivateChatUIModel, newItem: PrivateChatUIModel) =
            oldItem.privateChat.id == newItem.privateChat.id

        override fun areContentsTheSame(oldItem: PrivateChatUIModel, newItem: PrivateChatUIModel) : Boolean {
            return if (oldItem is PrivateChatUIModel.ChatUIModel && newItem is PrivateChatUIModel.ChatUIModel)
                oldItem.privateChat == newItem.privateChat && oldItem.selected==newItem.selected
            else false
        }
    }
}